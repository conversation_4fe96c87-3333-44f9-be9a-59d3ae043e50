{"version": 3, "file": "index-no-eval.min.js", "sources": ["../unpack.js", "../pack.js", "../iterators.js", "../index.js"], "sourcesContent": ["var decoder\ntry {\n\tdecoder = new TextDecoder()\n} catch(error) {}\nvar src\nvar srcEnd\nvar position = 0\nvar alreadySet\nconst EMPTY_ARRAY = []\nvar strings = EMPTY_ARRAY\nvar stringPosition = 0\nvar currentUnpackr = {}\nvar currentStructures\nvar srcString\nvar srcStringStart = 0\nvar srcStringEnd = 0\nvar bundledStrings\nvar referenceMap\nvar currentExtensions = []\nvar dataView\nvar defaultOptions = {\n\tuseRecords: false,\n\tmapsAsObjects: true\n}\nexport class C1Type {}\nexport const C1 = new C1Type()\nC1.name = 'MessagePack 0xC1'\nvar sequentialMode = false\nvar inlineObjectReadThreshold = 2\nvar readStruct, onLoadedStructures, onSaveState\nvar BlockedFunction // we use search and replace to change the next call to BlockedFunction to avoid CSP issues for\n// no-eval build\ntry {\n\tnew Function('')\n} catch(error) {\n\t// if eval variants are not supported, do not create inline object readers ever\n\tinlineObjectReadThreshold = Infinity\n}\n\nexport class Unpackr {\n\tconstructor(options) {\n\t\tif (options) {\n\t\t\tif (options.useRecords === false && options.mapsAsObjects === undefined)\n\t\t\t\toptions.mapsAsObjects = true\n\t\t\tif (options.sequential && options.trusted !== false) {\n\t\t\t\toptions.trusted = true;\n\t\t\t\tif (!options.structures && options.useRecords != false) {\n\t\t\t\t\toptions.structures = []\n\t\t\t\t\tif (!options.maxSharedStructures)\n\t\t\t\t\t\toptions.maxSharedStructures = 0\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (options.structures)\n\t\t\t\toptions.structures.sharedLength = options.structures.length\n\t\t\telse if (options.getStructures) {\n\t\t\t\t(options.structures = []).uninitialized = true // this is what we use to denote an uninitialized structures\n\t\t\t\toptions.structures.sharedLength = 0\n\t\t\t}\n\t\t\tif (options.int64AsNumber) {\n\t\t\t\toptions.int64AsType = 'number'\n\t\t\t}\n\t\t}\n\t\tObject.assign(this, options)\n\t}\n\tunpack(source, options) {\n\t\tif (src) {\n\t\t\t// re-entrant execution, save the state and restore it after we do this unpack\n\t\t\treturn saveState(() => {\n\t\t\t\tclearSource()\n\t\t\t\treturn this ? this.unpack(source, options) : Unpackr.prototype.unpack.call(defaultOptions, source, options)\n\t\t\t})\n\t\t}\n\t\tif (!source.buffer && source.constructor === ArrayBuffer)\n\t\t\tsource = typeof Buffer !== 'undefined' ? Buffer.from(source) : new Uint8Array(source);\n\t\tif (typeof options === 'object') {\n\t\t\tsrcEnd = options.end || source.length\n\t\t\tposition = options.start || 0\n\t\t} else {\n\t\t\tposition = 0\n\t\t\tsrcEnd = options > -1 ? options : source.length\n\t\t}\n\t\tstringPosition = 0\n\t\tsrcStringEnd = 0\n\t\tsrcString = null\n\t\tstrings = EMPTY_ARRAY\n\t\tbundledStrings = null\n\t\tsrc = source\n\t\t// this provides cached access to the data view for a buffer if it is getting reused, which is a recommend\n\t\t// technique for getting data from a database where it can be copied into an existing buffer instead of creating\n\t\t// new ones\n\t\ttry {\n\t\t\tdataView = source.dataView || (source.dataView = new DataView(source.buffer, source.byteOffset, source.byteLength))\n\t\t} catch(error) {\n\t\t\t// if it doesn't have a buffer, maybe it is the wrong type of object\n\t\t\tsrc = null\n\t\t\tif (source instanceof Uint8Array)\n\t\t\t\tthrow error\n\t\t\tthrow new Error('Source must be a Uint8Array or Buffer but was a ' + ((source && typeof source == 'object') ? source.constructor.name : typeof source))\n\t\t}\n\t\tif (this instanceof Unpackr) {\n\t\t\tcurrentUnpackr = this\n\t\t\tif (this.structures) {\n\t\t\t\tcurrentStructures = this.structures\n\t\t\t\treturn checkedRead(options)\n\t\t\t} else if (!currentStructures || currentStructures.length > 0) {\n\t\t\t\tcurrentStructures = []\n\t\t\t}\n\t\t} else {\n\t\t\tcurrentUnpackr = defaultOptions\n\t\t\tif (!currentStructures || currentStructures.length > 0)\n\t\t\t\tcurrentStructures = []\n\t\t}\n\t\treturn checkedRead(options)\n\t}\n\tunpackMultiple(source, forEach) {\n\t\tlet values, lastPosition = 0\n\t\ttry {\n\t\t\tsequentialMode = true\n\t\t\tlet size = source.length\n\t\t\tlet value = this ? this.unpack(source, size) : defaultUnpackr.unpack(source, size)\n\t\t\tif (forEach) {\n\t\t\t\tif (forEach(value, lastPosition, position) === false) return;\n\t\t\t\twhile(position < size) {\n\t\t\t\t\tlastPosition = position\n\t\t\t\t\tif (forEach(checkedRead(), lastPosition, position) === false) {\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tvalues = [ value ]\n\t\t\t\twhile(position < size) {\n\t\t\t\t\tlastPosition = position\n\t\t\t\t\tvalues.push(checkedRead())\n\t\t\t\t}\n\t\t\t\treturn values\n\t\t\t}\n\t\t} catch(error) {\n\t\t\terror.lastPosition = lastPosition\n\t\t\terror.values = values\n\t\t\tthrow error\n\t\t} finally {\n\t\t\tsequentialMode = false\n\t\t\tclearSource()\n\t\t}\n\t}\n\t_mergeStructures(loadedStructures, existingStructures) {\n\t\tif (onLoadedStructures)\n\t\t\tloadedStructures = onLoadedStructures.call(this, loadedStructures);\n\t\tloadedStructures = loadedStructures || []\n\t\tif (Object.isFrozen(loadedStructures))\n\t\t\tloadedStructures = loadedStructures.map(structure => structure.slice(0))\n\t\tfor (let i = 0, l = loadedStructures.length; i < l; i++) {\n\t\t\tlet structure = loadedStructures[i]\n\t\t\tif (structure) {\n\t\t\t\tstructure.isShared = true\n\t\t\t\tif (i >= 32)\n\t\t\t\t\tstructure.highByte = (i - 32) >> 5\n\t\t\t}\n\t\t}\n\t\tloadedStructures.sharedLength = loadedStructures.length\n\t\tfor (let id in existingStructures || []) {\n\t\t\tif (id >= 0) {\n\t\t\t\tlet structure = loadedStructures[id]\n\t\t\t\tlet existing = existingStructures[id]\n\t\t\t\tif (existing) {\n\t\t\t\t\tif (structure)\n\t\t\t\t\t\t(loadedStructures.restoreStructures || (loadedStructures.restoreStructures = []))[id] = structure\n\t\t\t\t\tloadedStructures[id] = existing\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn this.structures = loadedStructures\n\t}\n\tdecode(source, options) {\n\t\treturn this.unpack(source, options)\n\t}\n}\nexport function getPosition() {\n\treturn position\n}\nexport function checkedRead(options) {\n\ttry {\n\t\tif (!currentUnpackr.trusted && !sequentialMode) {\n\t\t\tlet sharedLength = currentStructures.sharedLength || 0\n\t\t\tif (sharedLength < currentStructures.length)\n\t\t\t\tcurrentStructures.length = sharedLength\n\t\t}\n\t\tlet result\n\t\tif (currentUnpackr.randomAccessStructure && src[position] < 0x40 && src[position] >= 0x20 && readStruct) {\n\t\t\tresult = readStruct(src, position, srcEnd, currentUnpackr)\n\t\t\tsrc = null // dispose of this so that recursive unpack calls don't save state\n\t\t\tif (!(options && options.lazy) && result)\n\t\t\t\tresult = result.toJSON()\n\t\t\tposition = srcEnd\n\t\t} else\n\t\t\tresult = read()\n\t\tif (bundledStrings) { // bundled strings to skip past\n\t\t\tposition = bundledStrings.postBundlePosition\n\t\t\tbundledStrings = null\n\t\t}\n\t\tif (sequentialMode)\n\t\t\t// we only need to restore the structures if there was an error, but if we completed a read,\n\t\t\t// we can clear this out and keep the structures we read\n\t\t\tcurrentStructures.restoreStructures = null\n\n\t\tif (position == srcEnd) {\n\t\t\t// finished reading this source, cleanup references\n\t\t\tif (currentStructures && currentStructures.restoreStructures)\n\t\t\t\trestoreStructures()\n\t\t\tcurrentStructures = null\n\t\t\tsrc = null\n\t\t\tif (referenceMap)\n\t\t\t\treferenceMap = null\n\t\t} else if (position > srcEnd) {\n\t\t\t// over read\n\t\t\tthrow new Error('Unexpected end of MessagePack data')\n\t\t} else if (!sequentialMode) {\n\t\t\tlet jsonView;\n\t\t\ttry {\n\t\t\t\tjsonView = JSON.stringify(result, (_, value) => typeof value === \"bigint\" ? `${value}n` : value).slice(0, 100)\n\t\t\t} catch(error) {\n\t\t\t\tjsonView = '(JSON view not available ' + error + ')'\n\t\t\t}\n\t\t\tthrow new Error('Data read, but end of buffer not reached ' + jsonView)\n\t\t}\n\t\t// else more to read, but we are reading sequentially, so don't clear source yet\n\t\treturn result\n\t} catch(error) {\n\t\tif (currentStructures && currentStructures.restoreStructures)\n\t\t\trestoreStructures()\n\t\tclearSource()\n\t\tif (error instanceof RangeError || error.message.startsWith('Unexpected end of buffer') || position > srcEnd) {\n\t\t\terror.incomplete = true\n\t\t}\n\t\tthrow error\n\t}\n}\n\nfunction restoreStructures() {\n\tfor (let id in currentStructures.restoreStructures) {\n\t\tcurrentStructures[id] = currentStructures.restoreStructures[id]\n\t}\n\tcurrentStructures.restoreStructures = null\n}\n\nexport function read() {\n\tlet token = src[position++]\n\tif (token < 0xa0) {\n\t\tif (token < 0x80) {\n\t\t\tif (token < 0x40)\n\t\t\t\treturn token\n\t\t\telse {\n\t\t\t\tlet structure = currentStructures[token & 0x3f] ||\n\t\t\t\t\tcurrentUnpackr.getStructures && loadStructures()[token & 0x3f]\n\t\t\t\tif (structure) {\n\t\t\t\t\tif (!structure.read) {\n\t\t\t\t\t\tstructure.read = createStructureReader(structure, token & 0x3f)\n\t\t\t\t\t}\n\t\t\t\t\treturn structure.read()\n\t\t\t\t} else\n\t\t\t\t\treturn token\n\t\t\t}\n\t\t} else if (token < 0x90) {\n\t\t\t// map\n\t\t\ttoken -= 0x80\n\t\t\tif (currentUnpackr.mapsAsObjects) {\n\t\t\t\tlet object = {}\n\t\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\t\tlet key = readKey()\n\t\t\t\t\tif (key === '__proto__')\n\t\t\t\t\t\tkey = '__proto_'\n\t\t\t\t\tobject[key] = read()\n\t\t\t\t}\n\t\t\t\treturn object\n\t\t\t} else {\n\t\t\t\tlet map = new Map()\n\t\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\t\tmap.set(read(), read())\n\t\t\t\t}\n\t\t\t\treturn map\n\t\t\t}\n\t\t} else {\n\t\t\ttoken -= 0x90\n\t\t\tlet array = new Array(token)\n\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\tarray[i] = read()\n\t\t\t}\n\t\t\tif (currentUnpackr.freezeData)\n\t\t\t\treturn Object.freeze(array)\n\t\t\treturn array\n\t\t}\n\t} else if (token < 0xc0) {\n\t\t// fixstr\n\t\tlet length = token - 0xa0\n\t\tif (srcStringEnd >= position) {\n\t\t\treturn srcString.slice(position - srcStringStart, (position += length) - srcStringStart)\n\t\t}\n\t\tif (srcStringEnd == 0 && srcEnd < 140) {\n\t\t\t// for small blocks, avoiding the overhead of the extract call is helpful\n\t\t\tlet string = length < 16 ? shortStringInJS(length) : longStringInJS(length)\n\t\t\tif (string != null)\n\t\t\t\treturn string\n\t\t}\n\t\treturn readFixedString(length)\n\t} else {\n\t\tlet value\n\t\tswitch (token) {\n\t\t\tcase 0xc0: return null\n\t\t\tcase 0xc1:\n\t\t\t\tif (bundledStrings) {\n\t\t\t\t\tvalue = read() // followed by the length of the string in characters (not bytes!)\n\t\t\t\t\tif (value > 0)\n\t\t\t\t\t\treturn bundledStrings[1].slice(bundledStrings.position1, bundledStrings.position1 += value)\n\t\t\t\t\telse\n\t\t\t\t\t\treturn bundledStrings[0].slice(bundledStrings.position0, bundledStrings.position0 -= value)\n\t\t\t\t}\n\t\t\t\treturn C1; // \"never-used\", return special object to denote that\n\t\t\tcase 0xc2: return false\n\t\t\tcase 0xc3: return true\n\t\t\tcase 0xc4:\n\t\t\t\t// bin 8\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (value === undefined)\n\t\t\t\t\tthrow new Error('Unexpected end of buffer')\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc5:\n\t\t\t\t// bin 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc6:\n\t\t\t\t// bin 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc7:\n\t\t\t\t// ext 8\n\t\t\t\treturn readExt(src[position++])\n\t\t\tcase 0xc8:\n\t\t\t\t// ext 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readExt(value)\n\t\t\tcase 0xc9:\n\t\t\t\t// ext 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readExt(value)\n\t\t\tcase 0xca:\n\t\t\t\tvalue = dataView.getFloat32(position)\n\t\t\t\tif (currentUnpackr.useFloat32 > 2) {\n\t\t\t\t\t// this does rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\tlet multiplier = mult10[((src[position] & 0x7f) << 1) | (src[position + 1] >> 7)]\n\t\t\t\t\tposition += 4\n\t\t\t\t\treturn ((multiplier * value + (value > 0 ? 0.5 : -0.5)) >> 0) / multiplier\n\t\t\t\t}\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xcb:\n\t\t\t\tvalue = dataView.getFloat64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\t\t\t// uint handlers\n\t\t\tcase 0xcc:\n\t\t\t\treturn src[position++]\n\t\t\tcase 0xcd:\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn value\n\t\t\tcase 0xce:\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xcf:\n\t\t\t\tif (currentUnpackr.int64AsType === 'number') {\n\t\t\t\t\tvalue = dataView.getUint32(position) * 0x100000000\n\t\t\t\t\tvalue += dataView.getUint32(position + 4)\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'string') {\n\t\t\t\t\tvalue = dataView.getBigUint64(position).toString()\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'auto') {\n\t\t\t\t\tvalue = dataView.getBigUint64(position)\n\t\t\t\t\tif (value<=BigInt(2)<<BigInt(52)) value=Number(value)\n\t\t\t\t} else\n\t\t\t\t\tvalue = dataView.getBigUint64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\n\t\t\t// int handlers\n\t\t\tcase 0xd0:\n\t\t\t\treturn dataView.getInt8(position++)\n\t\t\tcase 0xd1:\n\t\t\t\tvalue = dataView.getInt16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn value\n\t\t\tcase 0xd2:\n\t\t\t\tvalue = dataView.getInt32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xd3:\n\t\t\t\tif (currentUnpackr.int64AsType === 'number') {\n\t\t\t\t\tvalue = dataView.getInt32(position) * 0x100000000\n\t\t\t\t\tvalue += dataView.getUint32(position + 4)\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'string') {\n\t\t\t\t\tvalue = dataView.getBigInt64(position).toString()\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'auto') {\n\t\t\t\t\tvalue = dataView.getBigInt64(position)\n\t\t\t\t\tif (value>=BigInt(-2)<<BigInt(52)&&value<=BigInt(2)<<BigInt(52)) value=Number(value)\n\t\t\t\t} else\n\t\t\t\t\tvalue = dataView.getBigInt64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\n\t\t\tcase 0xd4:\n\t\t\t\t// fixext 1\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (value == 0x72) {\n\t\t\t\t\treturn recordDefinition(src[position++] & 0x3f)\n\t\t\t\t} else {\n\t\t\t\t\tlet extension = currentExtensions[value]\n\t\t\t\t\tif (extension) {\n\t\t\t\t\t\tif (extension.read) {\n\t\t\t\t\t\t\tposition++ // skip filler byte\n\t\t\t\t\t\t\treturn extension.read(read())\n\t\t\t\t\t\t} else if (extension.noBuffer) {\n\t\t\t\t\t\t\tposition++ // skip filler byte\n\t\t\t\t\t\t\treturn extension()\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\treturn extension(src.subarray(position, ++position))\n\t\t\t\t\t} else\n\t\t\t\t\t\tthrow new Error('Unknown extension ' + value)\n\t\t\t\t}\n\t\t\tcase 0xd5:\n\t\t\t\t// fixext 2\n\t\t\t\tvalue = src[position]\n\t\t\t\tif (value == 0x72) {\n\t\t\t\t\tposition++\n\t\t\t\t\treturn recordDefinition(src[position++] & 0x3f, src[position++])\n\t\t\t\t} else\n\t\t\t\t\treturn readExt(2)\n\t\t\tcase 0xd6:\n\t\t\t\t// fixext 4\n\t\t\t\treturn readExt(4)\n\t\t\tcase 0xd7:\n\t\t\t\t// fixext 8\n\t\t\t\treturn readExt(8)\n\t\t\tcase 0xd8:\n\t\t\t\t// fixext 16\n\t\t\t\treturn readExt(16)\n\t\t\tcase 0xd9:\n\t\t\t// str 8\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString8(value)\n\t\t\tcase 0xda:\n\t\t\t// str 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString16(value)\n\t\t\tcase 0xdb:\n\t\t\t// str 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString32(value)\n\t\t\tcase 0xdc:\n\t\t\t// array 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readArray(value)\n\t\t\tcase 0xdd:\n\t\t\t// array 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readArray(value)\n\t\t\tcase 0xde:\n\t\t\t// map 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readMap(value)\n\t\t\tcase 0xdf:\n\t\t\t// map 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readMap(value)\n\t\t\tdefault: // negative int\n\t\t\t\tif (token >= 0xe0)\n\t\t\t\t\treturn token - 0x100\n\t\t\t\tif (token === undefined) {\n\t\t\t\t\tlet error = new Error('Unexpected end of MessagePack data')\n\t\t\t\t\terror.incomplete = true\n\t\t\t\t\tthrow error\n\t\t\t\t}\n\t\t\t\tthrow new Error('Unknown MessagePack token ' + token)\n\n\t\t}\n\t}\n}\nconst validName = /^[a-zA-Z_$][a-zA-Z\\d_$]*$/\nfunction createStructureReader(structure, firstId) {\n\tfunction readObject() {\n\t\t// This initial function is quick to instantiate, but runs slower. After several iterations pay the cost to build the faster function\n\t\tif (readObject.count++ > inlineObjectReadThreshold) {\n\t\t\tlet readObject = structure.read = (new Function('r', 'return function(){return ' + (currentUnpackr.freezeData ? 'Object.freeze' : '') +\n\t\t\t\t'({' + structure.map(key => key === '__proto__' ? '__proto_:r()' : validName.test(key) ? key + ':r()' : ('[' + JSON.stringify(key) + ']:r()')).join(',') + '})}'))(read)\n\t\t\tif (structure.highByte === 0)\n\t\t\t\tstructure.read = createSecondByteReader(firstId, structure.read)\n\t\t\treturn readObject() // second byte is already read, if there is one so immediately read object\n\t\t}\n\t\tlet object = {}\n\t\tfor (let i = 0, l = structure.length; i < l; i++) {\n\t\t\tlet key = structure[i]\n\t\t\tif (key === '__proto__')\n\t\t\t\tkey = '__proto_'\n\t\t\tobject[key] = read()\n\t\t}\n\t\tif (currentUnpackr.freezeData)\n\t\t\treturn Object.freeze(object);\n\t\treturn object\n\t}\n\treadObject.count = 0\n\tif (structure.highByte === 0) {\n\t\treturn createSecondByteReader(firstId, readObject)\n\t}\n\treturn readObject\n}\n\nconst createSecondByteReader = (firstId, read0) => {\n\treturn function() {\n\t\tlet highByte = src[position++]\n\t\tif (highByte === 0)\n\t\t\treturn read0()\n\t\tlet id = firstId < 32 ? -(firstId + (highByte << 5)) : firstId + (highByte << 5)\n\t\tlet structure = currentStructures[id] || loadStructures()[id]\n\t\tif (!structure) {\n\t\t\tthrow new Error('Record id is not defined for ' + id)\n\t\t}\n\t\tif (!structure.read)\n\t\t\tstructure.read = createStructureReader(structure, firstId)\n\t\treturn structure.read()\n\t}\n}\n\nexport function loadStructures() {\n\tlet loadedStructures = saveState(() => {\n\t\t// save the state in case getStructures modifies our buffer\n\t\tsrc = null\n\t\treturn currentUnpackr.getStructures()\n\t})\n\treturn currentStructures = currentUnpackr._mergeStructures(loadedStructures, currentStructures)\n}\n\nvar readFixedString = readStringJS\nvar readString8 = readStringJS\nvar readString16 = readStringJS\nvar readString32 = readStringJS\nexport let isNativeAccelerationEnabled = false\n\nexport function setExtractor(extractStrings) {\n\tisNativeAccelerationEnabled = true\n\treadFixedString = readString(1)\n\treadString8 = readString(2)\n\treadString16 = readString(3)\n\treadString32 = readString(5)\n\tfunction readString(headerLength) {\n\t\treturn function readString(length) {\n\t\t\tlet string = strings[stringPosition++]\n\t\t\tif (string == null) {\n\t\t\t\tif (bundledStrings)\n\t\t\t\t\treturn readStringJS(length)\n\t\t\t\tlet byteOffset = src.byteOffset\n\t\t\t\tlet extraction = extractStrings(position - headerLength + byteOffset, srcEnd + byteOffset, src.buffer)\n\t\t\t\tif (typeof extraction == 'string') {\n\t\t\t\t\tstring = extraction\n\t\t\t\t\tstrings = EMPTY_ARRAY\n\t\t\t\t} else {\n\t\t\t\t\tstrings = extraction\n\t\t\t\t\tstringPosition = 1\n\t\t\t\t\tsrcStringEnd = 1 // even if a utf-8 string was decoded, must indicate we are in the midst of extracted strings and can't skip strings\n\t\t\t\t\tstring = strings[0]\n\t\t\t\t\tif (string === undefined)\n\t\t\t\t\t\tthrow new Error('Unexpected end of buffer')\n\t\t\t\t}\n\t\t\t}\n\t\t\tlet srcStringLength = string.length\n\t\t\tif (srcStringLength <= length) {\n\t\t\t\tposition += length\n\t\t\t\treturn string\n\t\t\t}\n\t\t\tsrcString = string\n\t\t\tsrcStringStart = position\n\t\t\tsrcStringEnd = position + srcStringLength\n\t\t\tposition += length\n\t\t\treturn string.slice(0, length) // we know we just want the beginning\n\t\t}\n\t}\n}\nfunction readStringJS(length) {\n\tlet result\n\tif (length < 16) {\n\t\tif (result = shortStringInJS(length))\n\t\t\treturn result\n\t}\n\tif (length > 64 && decoder)\n\t\treturn decoder.decode(src.subarray(position, position += length))\n\tconst end = position + length\n\tconst units = []\n\tresult = ''\n\twhile (position < end) {\n\t\tconst byte1 = src[position++]\n\t\tif ((byte1 & 0x80) === 0) {\n\t\t\t// 1 byte\n\t\t\tunits.push(byte1)\n\t\t} else if ((byte1 & 0xe0) === 0xc0) {\n\t\t\t// 2 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tunits.push(((byte1 & 0x1f) << 6) | byte2)\n\t\t} else if ((byte1 & 0xf0) === 0xe0) {\n\t\t\t// 3 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tconst byte3 = src[position++] & 0x3f\n\t\t\tunits.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3)\n\t\t} else if ((byte1 & 0xf8) === 0xf0) {\n\t\t\t// 4 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tconst byte3 = src[position++] & 0x3f\n\t\t\tconst byte4 = src[position++] & 0x3f\n\t\t\tlet unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4\n\t\t\tif (unit > 0xffff) {\n\t\t\t\tunit -= 0x10000\n\t\t\t\tunits.push(((unit >>> 10) & 0x3ff) | 0xd800)\n\t\t\t\tunit = 0xdc00 | (unit & 0x3ff)\n\t\t\t}\n\t\t\tunits.push(unit)\n\t\t} else {\n\t\t\tunits.push(byte1)\n\t\t}\n\n\t\tif (units.length >= 0x1000) {\n\t\t\tresult += fromCharCode.apply(String, units)\n\t\t\tunits.length = 0\n\t\t}\n\t}\n\n\tif (units.length > 0) {\n\t\tresult += fromCharCode.apply(String, units)\n\t}\n\n\treturn result\n}\nexport function readString(source, start, length) {\n\tlet existingSrc = src;\n\tsrc = source;\n\tposition = start;\n\ttry {\n\t\treturn readStringJS(length);\n\t} finally {\n\t\tsrc = existingSrc;\n\t}\n}\n\nfunction readArray(length) {\n\tlet array = new Array(length)\n\tfor (let i = 0; i < length; i++) {\n\t\tarray[i] = read()\n\t}\n\tif (currentUnpackr.freezeData)\n\t\treturn Object.freeze(array)\n\treturn array\n}\n\nfunction readMap(length) {\n\tif (currentUnpackr.mapsAsObjects) {\n\t\tlet object = {}\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tlet key = readKey()\n\t\t\tif (key === '__proto__')\n\t\t\t\tkey = '__proto_';\n\t\t\tobject[key] = read()\n\t\t}\n\t\treturn object\n\t} else {\n\t\tlet map = new Map()\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tmap.set(read(), read())\n\t\t}\n\t\treturn map\n\t}\n}\n\nvar fromCharCode = String.fromCharCode\nfunction longStringInJS(length) {\n\tlet start = position\n\tlet bytes = new Array(length)\n\tfor (let i = 0; i < length; i++) {\n\t\tconst byte = src[position++];\n\t\tif ((byte & 0x80) > 0) {\n\t\t\t\tposition = start\n\t\t\t\treturn\n\t\t\t}\n\t\t\tbytes[i] = byte\n\t\t}\n\t\treturn fromCharCode.apply(String, bytes)\n}\nfunction shortStringInJS(length) {\n\tif (length < 4) {\n\t\tif (length < 2) {\n\t\t\tif (length === 0)\n\t\t\t\treturn ''\n\t\t\telse {\n\t\t\t\tlet a = src[position++]\n\t\t\t\tif ((a & 0x80) > 1) {\n\t\t\t\t\tposition -= 1\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a)\n\t\t\t}\n\t\t} else {\n\t\t\tlet a = src[position++]\n\t\t\tlet b = src[position++]\n\t\t\tif ((a & 0x80) > 0 || (b & 0x80) > 0) {\n\t\t\t\tposition -= 2\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 3)\n\t\t\t\treturn fromCharCode(a, b)\n\t\t\tlet c = src[position++]\n\t\t\tif ((c & 0x80) > 0) {\n\t\t\t\tposition -= 3\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn fromCharCode(a, b, c)\n\t\t}\n\t} else {\n\t\tlet a = src[position++]\n\t\tlet b = src[position++]\n\t\tlet c = src[position++]\n\t\tlet d = src[position++]\n\t\tif ((a & 0x80) > 0 || (b & 0x80) > 0 || (c & 0x80) > 0 || (d & 0x80) > 0) {\n\t\t\tposition -= 4\n\t\t\treturn\n\t\t}\n\t\tif (length < 6) {\n\t\t\tif (length === 4)\n\t\t\t\treturn fromCharCode(a, b, c, d)\n\t\t\telse {\n\t\t\t\tlet e = src[position++]\n\t\t\t\tif ((e & 0x80) > 0) {\n\t\t\t\t\tposition -= 5\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a, b, c, d, e)\n\t\t\t}\n\t\t} else if (length < 8) {\n\t\t\tlet e = src[position++]\n\t\t\tlet f = src[position++]\n\t\t\tif ((e & 0x80) > 0 || (f & 0x80) > 0) {\n\t\t\t\tposition -= 6\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 7)\n\t\t\t\treturn fromCharCode(a, b, c, d, e, f)\n\t\t\tlet g = src[position++]\n\t\t\tif ((g & 0x80) > 0) {\n\t\t\t\tposition -= 7\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn fromCharCode(a, b, c, d, e, f, g)\n\t\t} else {\n\t\t\tlet e = src[position++]\n\t\t\tlet f = src[position++]\n\t\t\tlet g = src[position++]\n\t\t\tlet h = src[position++]\n\t\t\tif ((e & 0x80) > 0 || (f & 0x80) > 0 || (g & 0x80) > 0 || (h & 0x80) > 0) {\n\t\t\t\tposition -= 8\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 10) {\n\t\t\t\tif (length === 8)\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h)\n\t\t\t\telse {\n\t\t\t\t\tlet i = src[position++]\n\t\t\t\t\tif ((i & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 9\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i)\n\t\t\t\t}\n\t\t\t} else if (length < 12) {\n\t\t\t\tlet i = src[position++]\n\t\t\t\tlet j = src[position++]\n\t\t\t\tif ((i & 0x80) > 0 || (j & 0x80) > 0) {\n\t\t\t\t\tposition -= 10\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (length < 11)\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j)\n\t\t\t\tlet k = src[position++]\n\t\t\t\tif ((k & 0x80) > 0) {\n\t\t\t\t\tposition -= 11\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k)\n\t\t\t} else {\n\t\t\t\tlet i = src[position++]\n\t\t\t\tlet j = src[position++]\n\t\t\t\tlet k = src[position++]\n\t\t\t\tlet l = src[position++]\n\t\t\t\tif ((i & 0x80) > 0 || (j & 0x80) > 0 || (k & 0x80) > 0 || (l & 0x80) > 0) {\n\t\t\t\t\tposition -= 12\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (length < 14) {\n\t\t\t\t\tif (length === 12)\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l)\n\t\t\t\t\telse {\n\t\t\t\t\t\tlet m = src[position++]\n\t\t\t\t\t\tif ((m & 0x80) > 0) {\n\t\t\t\t\t\t\tposition -= 13\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m)\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet m = src[position++]\n\t\t\t\t\tlet n = src[position++]\n\t\t\t\t\tif ((m & 0x80) > 0 || (n & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 14\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\tif (length < 15)\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m, n)\n\t\t\t\t\tlet o = src[position++]\n\t\t\t\t\tif ((o & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 15\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction readOnlyJSString() {\n\tlet token = src[position++]\n\tlet length\n\tif (token < 0xc0) {\n\t\t// fixstr\n\t\tlength = token - 0xa0\n\t} else {\n\t\tswitch(token) {\n\t\t\tcase 0xd9:\n\t\t\t// str 8\n\t\t\t\tlength = src[position++]\n\t\t\t\tbreak\n\t\t\tcase 0xda:\n\t\t\t// str 16\n\t\t\t\tlength = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\tbreak\n\t\t\tcase 0xdb:\n\t\t\t// str 32\n\t\t\t\tlength = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\tbreak\n\t\t\tdefault:\n\t\t\t\tthrow new Error('Expected string')\n\t\t}\n\t}\n\treturn readStringJS(length)\n}\n\n\nfunction readBin(length) {\n\treturn currentUnpackr.copyBuffers ?\n\t\t// specifically use the copying slice (not the node one)\n\t\tUint8Array.prototype.slice.call(src, position, position += length) :\n\t\tsrc.subarray(position, position += length)\n}\nfunction readExt(length) {\n\tlet type = src[position++]\n\tif (currentExtensions[type]) {\n\t\tlet end\n\t\treturn currentExtensions[type](src.subarray(position, end = (position += length)), (readPosition) => {\n\t\t\tposition = readPosition;\n\t\t\ttry {\n\t\t\t\treturn read();\n\t\t\t} finally {\n\t\t\t\tposition = end;\n\t\t\t}\n\t\t})\n\t}\n\telse\n\t\tthrow new Error('Unknown extension type ' + type)\n}\n\nvar keyCache = new Array(4096)\nfunction readKey() {\n\tlet length = src[position++]\n\tif (length >= 0xa0 && length < 0xc0) {\n\t\t// fixstr, potentially use key cache\n\t\tlength = length - 0xa0\n\t\tif (srcStringEnd >= position) // if it has been extracted, must use it (and faster anyway)\n\t\t\treturn srcString.slice(position - srcStringStart, (position += length) - srcStringStart)\n\t\telse if (!(srcStringEnd == 0 && srcEnd < 180))\n\t\t\treturn readFixedString(length)\n\t} else { // not cacheable, go back and do a standard read\n\t\tposition--\n\t\treturn asSafeString(read())\n\t}\n\tlet key = ((length << 5) ^ (length > 1 ? dataView.getUint16(position) : length > 0 ? src[position] : 0)) & 0xfff\n\tlet entry = keyCache[key]\n\tlet checkPosition = position\n\tlet end = position + length - 3\n\tlet chunk\n\tlet i = 0\n\tif (entry && entry.bytes == length) {\n\t\twhile (checkPosition < end) {\n\t\t\tchunk = dataView.getUint32(checkPosition)\n\t\t\tif (chunk != entry[i++]) {\n\t\t\t\tcheckPosition = 0x70000000\n\t\t\t\tbreak\n\t\t\t}\n\t\t\tcheckPosition += 4\n\t\t}\n\t\tend += 3\n\t\twhile (checkPosition < end) {\n\t\t\tchunk = src[checkPosition++]\n\t\t\tif (chunk != entry[i++]) {\n\t\t\t\tcheckPosition = 0x70000000\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\tif (checkPosition === end) {\n\t\t\tposition = checkPosition\n\t\t\treturn entry.string\n\t\t}\n\t\tend -= 3\n\t\tcheckPosition = position\n\t}\n\tentry = []\n\tkeyCache[key] = entry\n\tentry.bytes = length\n\twhile (checkPosition < end) {\n\t\tchunk = dataView.getUint32(checkPosition)\n\t\tentry.push(chunk)\n\t\tcheckPosition += 4\n\t}\n\tend += 3\n\twhile (checkPosition < end) {\n\t\tchunk = src[checkPosition++]\n\t\tentry.push(chunk)\n\t}\n\t// for small blocks, avoiding the overhead of the extract call is helpful\n\tlet string = length < 16 ? shortStringInJS(length) : longStringInJS(length)\n\tif (string != null)\n\t\treturn entry.string = string\n\treturn entry.string = readFixedString(length)\n}\n\nfunction asSafeString(property) {\n\t// protect against expensive (DoS) string conversions\n\tif (typeof property === 'string') return property;\n\tif (typeof property === 'number' || typeof property === 'boolean' || typeof property === 'bigint') return property.toString();\n\tif (property == null) return property + '';\n\tif (currentUnpackr.allowArraysInMapKeys && Array.isArray(property) && property.flat().every(item => ['string', 'number', 'boolean', 'bigint'].includes(typeof item))) {\n\t\treturn property.flat().toString();\n\t}\n\tthrow new Error(`Invalid property type for record: ${typeof property}`);\n}\n// the registration of the record definition extension (as \"r\")\nconst recordDefinition = (id, highByte) => {\n\tlet structure = read().map(asSafeString) // ensure that all keys are strings and\n\t// that the array is mutable\n\tlet firstByte = id\n\tif (highByte !== undefined) {\n\t\tid = id < 32 ? -((highByte << 5) + id) : ((highByte << 5) + id)\n\t\tstructure.highByte = highByte\n\t}\n\tlet existingStructure = currentStructures[id]\n\t// If it is a shared structure, we need to restore any changes after reading.\n\t// Also in sequential mode, we may get incomplete reads and thus errors, and we need to restore\n\t// to the state prior to an incomplete read in order to properly resume.\n\tif (existingStructure && (existingStructure.isShared || sequentialMode)) {\n\t\t(currentStructures.restoreStructures || (currentStructures.restoreStructures = []))[id] = existingStructure\n\t}\n\tcurrentStructures[id] = structure\n\tstructure.read = createStructureReader(structure, firstByte)\n\treturn structure.read()\n}\ncurrentExtensions[0] = () => {} // notepack defines extension 0 to mean undefined, so use that as the default here\ncurrentExtensions[0].noBuffer = true\n\ncurrentExtensions[0x42] = data => {\n\tlet headLength = (data.byteLength % 8) || 8\n\tlet head = BigInt(data[0] & 0x80 ? data[0] - 0x100 : data[0])\n\tfor (let i = 1; i < headLength; i++) {\n\t\thead <<= BigInt(8)\n\t\thead += BigInt(data[i])\n\t}\n\tif (data.byteLength !== headLength) {\n\t\tlet view = new DataView(data.buffer, data.byteOffset, data.byteLength)\n\t\tlet decode = (start, end) => {\n\t\t\tlet length = end - start\n\t\t\tif (length <= 40) {\n\t\t\t\tlet out = view.getBigUint64(start)\n\t\t\t\tfor (let i = start + 8; i < end; i += 8) {\n\t\t\t\t\tout <<= BigInt(64n)\n\t\t\t\t\tout |= view.getBigUint64(i)\n\t\t\t\t}\n\t\t\t\treturn out\n\t\t\t}\n\t\t\t// if (length === 8) return view.getBigUint64(start)\n\t\t\tlet middle = start + (length >> 4 << 3)\n\t\t\tlet left = decode(start, middle)\n\t\t\tlet right = decode(middle, end)\n\t\t\treturn (left << BigInt((end - middle) * 8)) | right\n\t\t}\n\t\thead = (head << BigInt((view.byteLength - headLength) * 8)) | decode(headLength, view.byteLength)\n\t}\n\treturn head\n}\n\nlet errors = {\n\tError, EvalError, RangeError, ReferenceError, SyntaxError, TypeError, URIError, AggregateError: typeof AggregateError === 'function' ? AggregateError : null,\n}\ncurrentExtensions[0x65] = () => {\n\tlet data = read()\n\tif (!errors[data[0]]) {\n\t\tlet error = Error(data[1], { cause: data[2] })\n\t\terror.name = data[0]\n\t\treturn error\n\t}\n\treturn errors[data[0]](data[1], { cause: data[2] })\n}\n\ncurrentExtensions[0x69] = (data) => {\n\t// id extension (for structured clones)\n\tif (currentUnpackr.structuredClone === false) throw new Error('Structured clone extension is disabled')\n\tlet id = dataView.getUint32(position - 4)\n\tif (!referenceMap)\n\t\treferenceMap = new Map()\n\tlet token = src[position]\n\tlet target\n\t// TODO: handle any other types that can cycle and make the code more robust if there are other extensions\n\tif (token >= 0x90 && token < 0xa0 || token == 0xdc || token == 0xdd)\n\t\ttarget = []\n\telse if (token >= 0x80 && token < 0x90 || token == 0xde || token == 0xdf)\n\t\ttarget = new Map()\n\telse if ((token >= 0xc7 && token <= 0xc9 || token >= 0xd4 && token <= 0xd8) && src[position + 1] === 0x73)\n\t\ttarget = new Set()\n\telse\n\t\ttarget = {}\n\n\tlet refEntry = { target } // a placeholder object\n\treferenceMap.set(id, refEntry)\n\tlet targetProperties = read() // read the next value as the target object to id\n\tif (!refEntry.used) {\n\t\t// no cycle, can just use the returned read object\n\t\treturn refEntry.target = targetProperties // replace the placeholder with the real one\n\t} else {\n\t\t// there is a cycle, so we have to assign properties to original target\n\t\tObject.assign(target, targetProperties)\n\t}\n\n\t// copy over map/set entries if we're able to\n\tif (target instanceof Map)\n\t\tfor (let [k, v] of targetProperties.entries()) target.set(k, v)\n\tif (target instanceof Set)\n\t\tfor (let i of Array.from(targetProperties)) target.add(i)\n\treturn target\n}\n\ncurrentExtensions[0x70] = (data) => {\n\t// pointer extension (for structured clones)\n\tif (currentUnpackr.structuredClone === false) throw new Error('Structured clone extension is disabled')\n\tlet id = dataView.getUint32(position - 4)\n\tlet refEntry = referenceMap.get(id)\n\trefEntry.used = true\n\treturn refEntry.target\n}\n\ncurrentExtensions[0x73] = () => new Set(read())\n\nexport const typedArrays = ['Int8','Uint8','Uint8Clamped','Int16','Uint16','Int32','Uint32','Float32','Float64','BigInt64','BigUint64'].map(type => type + 'Array')\n\nlet glbl = typeof globalThis === 'object' ? globalThis : window;\ncurrentExtensions[0x74] = (data) => {\n\tlet typeCode = data[0]\n\t// we always have to slice to get a new ArrayBuffer that is aligned\n\tlet buffer = Uint8Array.prototype.slice.call(data, 1).buffer\n\n\tlet typedArrayName = typedArrays[typeCode]\n\tif (!typedArrayName) {\n\t\tif (typeCode === 16) return buffer\n\t\tif (typeCode === 17) return new DataView(buffer)\n\t\tthrow new Error('Could not find typed array for code ' + typeCode)\n\t}\n\treturn new glbl[typedArrayName](buffer)\n}\ncurrentExtensions[0x78] = () => {\n\tlet data = read()\n\treturn new RegExp(data[0], data[1])\n}\nconst TEMP_BUNDLE = []\ncurrentExtensions[0x62] = (data) => {\n\tlet dataSize = (data[0] << 24) + (data[1] << 16) + (data[2] << 8) + data[3]\n\tlet dataPosition = position\n\tposition += dataSize - data.length\n\tbundledStrings = TEMP_BUNDLE\n\tbundledStrings = [readOnlyJSString(), readOnlyJSString()]\n\tbundledStrings.position0 = 0\n\tbundledStrings.position1 = 0\n\tbundledStrings.postBundlePosition = position\n\tposition = dataPosition\n\treturn read()\n}\n\ncurrentExtensions[0xff] = (data) => {\n\t// 32-bit date extension\n\tif (data.length == 4)\n\t\treturn new Date((data[0] * 0x1000000 + (data[1] << 16) + (data[2] << 8) + data[3]) * 1000)\n\telse if (data.length == 8)\n\t\treturn new Date(\n\t\t\t((data[0] << 22) + (data[1] << 14) + (data[2] << 6) + (data[3] >> 2)) / 1000000 +\n\t\t\t((data[3] & 0x3) * 0x100000000 + data[4] * 0x1000000 + (data[5] << 16) + (data[6] << 8) + data[7]) * 1000)\n\telse if (data.length == 12)\n\t\treturn new Date(\n\t\t\t((data[0] << 24) + (data[1] << 16) + (data[2] << 8) + data[3]) / 1000000 +\n\t\t\t(((data[4] & 0x80) ? -0x1000000000000 : 0) + data[6] * 0x10000000000 + data[7] * 0x100000000 + data[8] * 0x1000000 + (data[9] << 16) + (data[10] << 8) + data[11]) * 1000)\n\telse\n\t\treturn new Date('invalid')\n}\n// registration of bulk record definition?\n// currentExtensions[0x52] = () =>\n\nfunction saveState(callback) {\n\tif (onSaveState)\n\t\tonSaveState();\n\tlet savedSrcEnd = srcEnd\n\tlet savedPosition = position\n\tlet savedStringPosition = stringPosition\n\tlet savedSrcStringStart = srcStringStart\n\tlet savedSrcStringEnd = srcStringEnd\n\tlet savedSrcString = srcString\n\tlet savedStrings = strings\n\tlet savedReferenceMap = referenceMap\n\tlet savedBundledStrings = bundledStrings\n\n\t// TODO: We may need to revisit this if we do more external calls to user code (since it could be slow)\n\tlet savedSrc = new Uint8Array(src.slice(0, srcEnd)) // we copy the data in case it changes while external data is processed\n\tlet savedStructures = currentStructures\n\tlet savedStructuresContents = currentStructures.slice(0, currentStructures.length)\n\tlet savedPackr = currentUnpackr\n\tlet savedSequentialMode = sequentialMode\n\tlet value = callback()\n\tsrcEnd = savedSrcEnd\n\tposition = savedPosition\n\tstringPosition = savedStringPosition\n\tsrcStringStart = savedSrcStringStart\n\tsrcStringEnd = savedSrcStringEnd\n\tsrcString = savedSrcString\n\tstrings = savedStrings\n\treferenceMap = savedReferenceMap\n\tbundledStrings = savedBundledStrings\n\tsrc = savedSrc\n\tsequentialMode = savedSequentialMode\n\tcurrentStructures = savedStructures\n\tcurrentStructures.splice(0, currentStructures.length, ...savedStructuresContents)\n\tcurrentUnpackr = savedPackr\n\tdataView = new DataView(src.buffer, src.byteOffset, src.byteLength)\n\treturn value\n}\nexport function clearSource() {\n\tsrc = null\n\treferenceMap = null\n\tcurrentStructures = null\n}\n\nexport function addExtension(extension) {\n\tif (extension.unpack)\n\t\tcurrentExtensions[extension.type] = extension.unpack\n\telse\n\t\tcurrentExtensions[extension.type] = extension\n}\n\nexport const mult10 = new Array(147) // this is a table matching binary exponents to the multiplier to determine significant digit rounding\nfor (let i = 0; i < 256; i++) {\n\tmult10[i] = +('1e' + Math.floor(45.15 - i * 0.30103))\n}\nexport const Decoder = Unpackr\nvar defaultUnpackr = new Unpackr({ useRecords: false })\nexport const unpack = defaultUnpackr.unpack\nexport const unpackMultiple = defaultUnpackr.unpackMultiple\nexport const decode = defaultUnpackr.unpack\nexport const FLOAT32_OPTIONS = {\n\tNEVER: 0,\n\tALWAYS: 1,\n\tDECIMAL_ROUND: 3,\n\tDECIMAL_FIT: 4\n}\nlet f32Array = new Float32Array(1)\nlet u8Array = new Uint8Array(f32Array.buffer, 0, 4)\nexport function roundFloat32(float32Number) {\n\tf32Array[0] = float32Number\n\tlet multiplier = mult10[((u8Array[3] & 0x7f) << 1) | (u8Array[2] >> 7)]\n\treturn ((multiplier * float32Number + (float32Number > 0 ? 0.5 : -0.5)) >> 0) / multiplier\n}\nexport function setReadStruct(updatedReadStruct, loadedStructs, saveState) {\n\treadStruct = updatedReadStruct;\n\tonLoadedStructures = loadedStructs;\n\tonSaveState = saveState;\n}\n", "import { Unpackr, mult10, C1Type, typedArrays, addExtension as unpackAddExtension } from './unpack.js'\nlet textEncoder\ntry {\n\ttextEncoder = new TextEncoder()\n} catch (error) {}\nlet extensions, extensionClasses\nconst hasNodeBuffer = typeof Buffer !== 'undefined'\nconst ByteArrayAllocate = hasNodeBuffer ?\n\tfunction(length) { return Buffer.allocUnsafeSlow(length) } : Uint8Array\nconst ByteArray = hasNodeBuffer ? Buffer : Uint8Array\nconst MAX_BUFFER_SIZE = hasNodeBuffer ? 0x100000000 : 0x7fd00000\nlet target, keysTarget\nlet targetView\nlet position = 0\nlet safeEnd\nlet bundledStrings = null\nlet writeStructSlots\nconst MAX_BUNDLE_SIZE = 0x5500 // maximum characters such that the encoded bytes fits in 16 bits.\nconst hasNonLatin = /[\\u0080-\\uFFFF]/\nexport const RECORD_SYMBOL = Symbol('record-id')\nexport class Packr extends Unpackr {\n\tconstructor(options) {\n\t\tsuper(options)\n\t\tthis.offset = 0\n\t\tlet typeBuffer\n\t\tlet start\n\t\tlet hasSharedUpdate\n\t\tlet structures\n\t\tlet referenceMap\n\t\tlet encodeUtf8 = ByteArray.prototype.utf8Write ? function(string, position) {\n\t\t\treturn target.utf8Write(string, position, target.byteLength - position)\n\t\t} : (textEncoder && textEncoder.encodeInto) ?\n\t\t\tfunction(string, position) {\n\t\t\t\treturn textEncoder.encodeInto(string, target.subarray(position)).written\n\t\t\t} : false\n\n\t\tlet packr = this\n\t\tif (!options)\n\t\t\toptions = {}\n\t\tlet isSequential = options && options.sequential\n\t\tlet hasSharedStructures = options.structures || options.saveStructures\n\t\tlet maxSharedStructures = options.maxSharedStructures\n\t\tif (maxSharedStructures == null)\n\t\t\tmaxSharedStructures = hasSharedStructures ? 32 : 0\n\t\tif (maxSharedStructures > 8160)\n\t\t\tthrow new Error('Maximum maxSharedStructure is 8160')\n\t\tif (options.structuredClone && options.moreTypes == undefined) {\n\t\t\tthis.moreTypes = true\n\t\t}\n\t\tlet maxOwnStructures = options.maxOwnStructures\n\t\tif (maxOwnStructures == null)\n\t\t\tmaxOwnStructures = hasSharedStructures ? 32 : 64\n\t\tif (!this.structures && options.useRecords != false)\n\t\t\tthis.structures = []\n\t\t// two byte record ids for shared structures\n\t\tlet useTwoByteRecords = maxSharedStructures > 32 || (maxOwnStructures + maxSharedStructures > 64)\n\t\tlet sharedLimitId = maxSharedStructures + 0x40\n\t\tlet maxStructureId = maxSharedStructures + maxOwnStructures + 0x40\n\t\tif (maxStructureId > 8256) {\n\t\t\tthrow new Error('Maximum maxSharedStructure + maxOwnStructure is 8192')\n\t\t}\n\t\tlet recordIdsToRemove = []\n\t\tlet transitionsCount = 0\n\t\tlet serializationsSinceTransitionRebuild = 0\n\n\t\tthis.pack = this.encode = function(value, encodeOptions) {\n\t\t\tif (!target) {\n\t\t\t\ttarget = new ByteArrayAllocate(8192)\n\t\t\t\ttargetView = target.dataView || (target.dataView = new DataView(target.buffer, 0, 8192))\n\t\t\t\tposition = 0\n\t\t\t}\n\t\t\tsafeEnd = target.length - 10\n\t\t\tif (safeEnd - position < 0x800) {\n\t\t\t\t// don't start too close to the end,\n\t\t\t\ttarget = new ByteArrayAllocate(target.length)\n\t\t\t\ttargetView = target.dataView || (target.dataView = new DataView(target.buffer, 0, target.length))\n\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\tposition = 0\n\t\t\t} else\n\t\t\t\tposition = (position + 7) & 0x7ffffff8 // Word align to make any future copying of this buffer faster\n\t\t\tstart = position\n\t\t\tif (encodeOptions & RESERVE_START_SPACE) position += (encodeOptions & 0xff)\n\t\t\treferenceMap = packr.structuredClone ? new Map() : null\n\t\t\tif (packr.bundleStrings && typeof value !== 'string') {\n\t\t\t\tbundledStrings = []\n\t\t\t\tbundledStrings.size = Infinity // force a new bundle start on first string\n\t\t\t} else\n\t\t\t\tbundledStrings = null\n\t\t\tstructures = packr.structures\n\t\t\tif (structures) {\n\t\t\t\tif (structures.uninitialized)\n\t\t\t\t\tstructures = packr._mergeStructures(packr.getStructures())\n\t\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\t\tif (sharedLength > maxSharedStructures) {\n\t\t\t\t\t//if (maxSharedStructures <= 32 && structures.sharedLength > 32) // TODO: could support this, but would need to update the limit ids\n\t\t\t\t\tthrow new Error('Shared structures is larger than maximum shared structures, try increasing maxSharedStructures to ' + structures.sharedLength)\n\t\t\t\t}\n\t\t\t\tif (!structures.transitions) {\n\t\t\t\t\t// rebuild our structure transitions\n\t\t\t\t\tstructures.transitions = Object.create(null)\n\t\t\t\t\tfor (let i = 0; i < sharedLength; i++) {\n\t\t\t\t\t\tlet keys = structures[i]\n\t\t\t\t\t\tif (!keys)\n\t\t\t\t\t\t\tcontinue\n\t\t\t\t\t\tlet nextTransition, transition = structures.transitions\n\t\t\t\t\t\tfor (let j = 0, l = keys.length; j < l; j++) {\n\t\t\t\t\t\t\tlet key = keys[j]\n\t\t\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttransition[RECORD_SYMBOL] = i + 0x40\n\t\t\t\t\t}\n\t\t\t\t\tthis.lastNamedStructuresLength = sharedLength\n\t\t\t\t}\n\t\t\t\tif (!isSequential) {\n\t\t\t\t\tstructures.nextId = sharedLength + 0x40\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (hasSharedUpdate)\n\t\t\t\thasSharedUpdate = false\n\t\t\tlet encodingError;\n\t\t\ttry {\n\t\t\t\tif (packr.randomAccessStructure && value && value.constructor && value.constructor === Object)\n\t\t\t\t\twriteStruct(value);\n\t\t\t\telse\n\t\t\t\t\tpack(value)\n\t\t\t\tlet lastBundle = bundledStrings;\n\t\t\t\tif (bundledStrings)\n\t\t\t\t\twriteBundles(start, pack, 0)\n\t\t\t\tif (referenceMap && referenceMap.idsToInsert) {\n\t\t\t\t\tlet idsToInsert = referenceMap.idsToInsert.sort((a, b) => a.offset > b.offset ? 1 : -1);\n\t\t\t\t\tlet i = idsToInsert.length;\n\t\t\t\t\tlet incrementPosition = -1;\n\t\t\t\t\twhile (lastBundle && i > 0) {\n\t\t\t\t\t\tlet insertionPoint = idsToInsert[--i].offset + start;\n\t\t\t\t\t\tif (insertionPoint < (lastBundle.stringsPosition + start) && incrementPosition === -1)\n\t\t\t\t\t\t\tincrementPosition = 0;\n\t\t\t\t\t\tif (insertionPoint > (lastBundle.position + start)) {\n\t\t\t\t\t\t\tif (incrementPosition >= 0)\n\t\t\t\t\t\t\t\tincrementPosition += 6;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (incrementPosition >= 0) {\n\t\t\t\t\t\t\t\t// update the bundle reference now\n\t\t\t\t\t\t\t\ttargetView.setUint32(lastBundle.position + start,\n\t\t\t\t\t\t\t\t\ttargetView.getUint32(lastBundle.position + start) + incrementPosition)\n\t\t\t\t\t\t\t\tincrementPosition = -1; // reset\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlastBundle = lastBundle.previous;\n\t\t\t\t\t\t\ti++;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (incrementPosition >= 0 && lastBundle) {\n\t\t\t\t\t\t// update the bundle reference now\n\t\t\t\t\t\ttargetView.setUint32(lastBundle.position + start,\n\t\t\t\t\t\t\ttargetView.getUint32(lastBundle.position + start) + incrementPosition)\n\t\t\t\t\t}\n\t\t\t\t\tposition += idsToInsert.length * 6;\n\t\t\t\t\tif (position > safeEnd)\n\t\t\t\t\t\tmakeRoom(position)\n\t\t\t\t\tpackr.offset = position\n\t\t\t\t\tlet serialized = insertIds(target.subarray(start, position), idsToInsert)\n\t\t\t\t\treferenceMap = null\n\t\t\t\t\treturn serialized\n\t\t\t\t}\n\t\t\t\tpackr.offset = position // update the offset so next serialization doesn't write over our buffer, but can continue writing to same buffer sequentially\n\t\t\t\tif (encodeOptions & REUSE_BUFFER_MODE) {\n\t\t\t\t\ttarget.start = start\n\t\t\t\t\ttarget.end = position\n\t\t\t\t\treturn target\n\t\t\t\t}\n\t\t\t\treturn target.subarray(start, position) // position can change if we call pack again in saveStructures, so we get the buffer now\n\t\t\t} catch(error) {\n\t\t\t\tencodingError = error;\n\t\t\t\tthrow error;\n\t\t\t} finally {\n\t\t\t\tif (structures) {\n\t\t\t\t\tresetStructures();\n\t\t\t\t\tif (hasSharedUpdate && packr.saveStructures) {\n\t\t\t\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\t\t\t\t// we can't rely on start/end with REUSE_BUFFER_MODE since they will (probably) change when we save\n\t\t\t\t\t\tlet returnBuffer = target.subarray(start, position)\n\t\t\t\t\t\tlet newSharedData = prepareStructures(structures, packr);\n\t\t\t\t\t\tif (!encodingError) { // TODO: If there is an encoding error, should make the structures as uninitialized so they get rebuilt next time\n\t\t\t\t\t\t\tif (packr.saveStructures(newSharedData, newSharedData.isCompatible) === false) {\n\t\t\t\t\t\t\t\t// get updated structures and try again if the update failed\n\t\t\t\t\t\t\t\treturn packr.pack(value, encodeOptions)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tpackr.lastNamedStructuresLength = sharedLength\n\t\t\t\t\t\t\t// don't keep large buffers around\n\t\t\t\t\t\t\tif (target.length > 0x40000000) target = null\n\t\t\t\t\t\t\treturn returnBuffer\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// don't keep large buffers around, they take too much memory and cause problems (limit at 1GB)\n\t\t\t\tif (target.length > 0x40000000) target = null\n\t\t\t\tif (encodeOptions & RESET_BUFFER_MODE)\n\t\t\t\t\tposition = start\n\t\t\t}\n\t\t}\n\t\tconst resetStructures = () => {\n\t\t\tif (serializationsSinceTransitionRebuild < 10)\n\t\t\t\tserializationsSinceTransitionRebuild++\n\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\tif (structures.length > sharedLength && !isSequential)\n\t\t\t\tstructures.length = sharedLength\n\t\t\tif (transitionsCount > 10000) {\n\t\t\t\t// force a rebuild occasionally after a lot of transitions so it can get cleaned up\n\t\t\t\tstructures.transitions = null\n\t\t\t\tserializationsSinceTransitionRebuild = 0\n\t\t\t\ttransitionsCount = 0\n\t\t\t\tif (recordIdsToRemove.length > 0)\n\t\t\t\t\trecordIdsToRemove = []\n\t\t\t} else if (recordIdsToRemove.length > 0 && !isSequential) {\n\t\t\t\tfor (let i = 0, l = recordIdsToRemove.length; i < l; i++) {\n\t\t\t\t\trecordIdsToRemove[i][RECORD_SYMBOL] = 0\n\t\t\t\t}\n\t\t\t\trecordIdsToRemove = []\n\t\t\t}\n\t\t}\n\t\tconst packArray = (value) => {\n\t\t\tvar length = value.length\n\t\t\tif (length < 0x10) {\n\t\t\t\ttarget[position++] = 0x90 | length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xdc\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xdd\n\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\tposition += 4\n\t\t\t}\n\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\tpack(value[i])\n\t\t\t}\n\t\t}\n\t\tconst pack = (value) => {\n\t\t\tif (position > safeEnd)\n\t\t\t\ttarget = makeRoom(position)\n\n\t\t\tvar type = typeof value\n\t\t\tvar length\n\t\t\tif (type === 'string') {\n\t\t\t\tlet strLength = value.length\n\t\t\t\tif (bundledStrings && strLength >= 4 && strLength < 0x1000) {\n\t\t\t\t\tif ((bundledStrings.size += strLength) > MAX_BUNDLE_SIZE) {\n\t\t\t\t\t\tlet extStart\n\t\t\t\t\t\tlet maxBytes = (bundledStrings[0] ? bundledStrings[0].length * 3 + bundledStrings[1].length : 0) + 10\n\t\t\t\t\t\tif (position + maxBytes > safeEnd)\n\t\t\t\t\t\t\ttarget = makeRoom(position + maxBytes)\n\t\t\t\t\t\tlet lastBundle\n\t\t\t\t\t\tif (bundledStrings.position) { // here we use the 0x62 extension to write the last bundle and reserve space for the reference pointer to the next/current bundle\n\t\t\t\t\t\t\tlastBundle = bundledStrings\n\t\t\t\t\t\t\ttarget[position] = 0xc8 // ext 16\n\t\t\t\t\t\t\tposition += 3 // reserve for the writing bundle size\n\t\t\t\t\t\t\ttarget[position++] = 0x62 // 'b'\n\t\t\t\t\t\t\textStart = position - start\n\t\t\t\t\t\t\tposition += 4 // reserve for writing bundle reference\n\t\t\t\t\t\t\twriteBundles(start, pack, 0) // write the last bundles\n\t\t\t\t\t\t\ttargetView.setUint16(extStart + start - 3, position - start - extStart)\n\t\t\t\t\t\t} else { // here we use the 0x62 extension just to reserve the space for the reference pointer to the bundle (will be updated once the bundle is written)\n\t\t\t\t\t\t\ttarget[position++] = 0xd6 // fixext 4\n\t\t\t\t\t\t\ttarget[position++] = 0x62 // 'b'\n\t\t\t\t\t\t\textStart = position - start\n\t\t\t\t\t\t\tposition += 4 // reserve for writing bundle reference\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbundledStrings = ['', ''] // create new ones\n\t\t\t\t\t\tbundledStrings.previous = lastBundle;\n\t\t\t\t\t\tbundledStrings.size = 0\n\t\t\t\t\t\tbundledStrings.position = extStart\n\t\t\t\t\t}\n\t\t\t\t\tlet twoByte = hasNonLatin.test(value)\n\t\t\t\t\tbundledStrings[twoByte ? 0 : 1] += value\n\t\t\t\t\ttarget[position++] = 0xc1\n\t\t\t\t\tpack(twoByte ? -strLength : strLength);\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet headerSize\n\t\t\t\t// first we estimate the header size, so we can write to the correct location\n\t\t\t\tif (strLength < 0x20) {\n\t\t\t\t\theaderSize = 1\n\t\t\t\t} else if (strLength < 0x100) {\n\t\t\t\t\theaderSize = 2\n\t\t\t\t} else if (strLength < 0x10000) {\n\t\t\t\t\theaderSize = 3\n\t\t\t\t} else {\n\t\t\t\t\theaderSize = 5\n\t\t\t\t}\n\t\t\t\tlet maxBytes = strLength * 3\n\t\t\t\tif (position + maxBytes > safeEnd)\n\t\t\t\t\ttarget = makeRoom(position + maxBytes)\n\n\t\t\t\tif (strLength < 0x40 || !encodeUtf8) {\n\t\t\t\t\tlet i, c1, c2, strPosition = position + headerSize\n\t\t\t\t\tfor (i = 0; i < strLength; i++) {\n\t\t\t\t\t\tc1 = value.charCodeAt(i)\n\t\t\t\t\t\tif (c1 < 0x80) {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1\n\t\t\t\t\t\t} else if (c1 < 0x800) {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 | 0xc0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\t(c1 & 0xfc00) === 0xd800 &&\n\t\t\t\t\t\t\t((c2 = value.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tc1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff)\n\t\t\t\t\t\t\ti++\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 18 | 0xf0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 12 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 12 | 0xe0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tlength = strPosition - position - headerSize\n\t\t\t\t} else {\n\t\t\t\t\tlength = encodeUtf8(value, position + headerSize)\n\t\t\t\t}\n\n\t\t\t\tif (length < 0x20) {\n\t\t\t\t\ttarget[position++] = 0xa0 | length\n\t\t\t\t} else if (length < 0x100) {\n\t\t\t\t\tif (headerSize < 2) {\n\t\t\t\t\t\ttarget.copyWithin(position + 2, position + 1, position + 1 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xd9\n\t\t\t\t\ttarget[position++] = length\n\t\t\t\t} else if (length < 0x10000) {\n\t\t\t\t\tif (headerSize < 3) {\n\t\t\t\t\t\ttarget.copyWithin(position + 3, position + 2, position + 2 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xda\n\t\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t\t} else {\n\t\t\t\t\tif (headerSize < 5) {\n\t\t\t\t\t\ttarget.copyWithin(position + 5, position + 3, position + 3 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xdb\n\t\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\t\tposition += 4\n\t\t\t\t}\n\t\t\t\tposition += length\n\t\t\t} else if (type === 'number') {\n\t\t\t\tif (value >>> 0 === value) {// positive integer, 32-bit or less\n\t\t\t\t\t// positive uint\n\t\t\t\t\tif (value < 0x20 || (value < 0x80 && this.useRecords === false) || (value < 0x40 && !this.randomAccessStructure)) {\n\t\t\t\t\t\ttarget[position++] = value\n\t\t\t\t\t} else if (value < 0x100) {\n\t\t\t\t\t\ttarget[position++] = 0xcc\n\t\t\t\t\t\ttarget[position++] = value\n\t\t\t\t\t} else if (value < 0x10000) {\n\t\t\t\t\t\ttarget[position++] = 0xcd\n\t\t\t\t\t\ttarget[position++] = value >> 8\n\t\t\t\t\t\ttarget[position++] = value & 0xff\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget[position++] = 0xce\n\t\t\t\t\t\ttargetView.setUint32(position, value)\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t}\n\t\t\t\t} else if (value >> 0 === value) { // negative integer\n\t\t\t\t\tif (value >= -0x20) {\n\t\t\t\t\t\ttarget[position++] = 0x100 + value\n\t\t\t\t\t} else if (value >= -0x80) {\n\t\t\t\t\t\ttarget[position++] = 0xd0\n\t\t\t\t\t\ttarget[position++] = value + 0x100\n\t\t\t\t\t} else if (value >= -0x8000) {\n\t\t\t\t\t\ttarget[position++] = 0xd1\n\t\t\t\t\t\ttargetView.setInt16(position, value)\n\t\t\t\t\t\tposition += 2\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget[position++] = 0xd2\n\t\t\t\t\t\ttargetView.setInt32(position, value)\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet useFloat32\n\t\t\t\t\tif ((useFloat32 = this.useFloat32) > 0 && value < 0x100000000 && value >= -0x80000000) {\n\t\t\t\t\t\ttarget[position++] = 0xca\n\t\t\t\t\t\ttargetView.setFloat32(position, value)\n\t\t\t\t\t\tlet xShifted\n\t\t\t\t\t\tif (useFloat32 < 4 ||\n\t\t\t\t\t\t\t\t// this checks for rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\t\t\t\t((xShifted = value * mult10[((target[position] & 0x7f) << 1) | (target[position + 1] >> 7)]) >> 0) === xShifted) {\n\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\tposition-- // move back into position for writing a double\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xcb\n\t\t\t\t\ttargetView.setFloat64(position, value)\n\t\t\t\t\tposition += 8\n\t\t\t\t}\n\t\t\t} else if (type === 'object' || type === 'function') {\n\t\t\t\tif (!value)\n\t\t\t\t\ttarget[position++] = 0xc0\n\t\t\t\telse {\n\t\t\t\t\tif (referenceMap) {\n\t\t\t\t\t\tlet referee = referenceMap.get(value)\n\t\t\t\t\t\tif (referee) {\n\t\t\t\t\t\t\tif (!referee.id) {\n\t\t\t\t\t\t\t\tlet idsToInsert = referenceMap.idsToInsert || (referenceMap.idsToInsert = [])\n\t\t\t\t\t\t\t\treferee.id = idsToInsert.push(referee)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttarget[position++] = 0xd6 // fixext 4\n\t\t\t\t\t\t\ttarget[position++] = 0x70 // \"p\" for pointer\n\t\t\t\t\t\t\ttargetView.setUint32(position, referee.id)\n\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\treferenceMap.set(value, { offset: position - start })\n\t\t\t\t\t}\n\t\t\t\t\tlet constructor = value.constructor\n\t\t\t\t\tif (constructor === Object) {\n\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t} else if (constructor === Array) {\n\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t} else if (constructor === Map) {\n\t\t\t\t\t\tif (this.mapAsEmptyObject) target[position++] = 0x80\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tlength = value.size\n\t\t\t\t\t\t\tif (length < 0x10) {\n\t\t\t\t\t\t\t\ttarget[position++] = 0x80 | length\n\t\t\t\t\t\t\t} else if (length < 0x10000) {\n\t\t\t\t\t\t\t\ttarget[position++] = 0xde\n\t\t\t\t\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\t\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttarget[position++] = 0xdf\n\t\t\t\t\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tfor (let [key, entryValue] of value) {\n\t\t\t\t\t\t\t\tpack(key)\n\t\t\t\t\t\t\t\tpack(entryValue)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tfor (let i = 0, l = extensions.length; i < l; i++) {\n\t\t\t\t\t\t\tlet extensionClass = extensionClasses[i]\n\t\t\t\t\t\t\tif (value instanceof extensionClass) {\n\t\t\t\t\t\t\t\tlet extension = extensions[i]\n\t\t\t\t\t\t\t\tif (extension.write) {\n\t\t\t\t\t\t\t\t\tif (extension.type) {\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = 0xd4 // one byte \"tag\" extension\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = extension.type\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tlet writeResult = extension.write.call(this, value)\n\t\t\t\t\t\t\t\t\tif (writeResult === value) { // avoid infinite recursion\n\t\t\t\t\t\t\t\t\t\tif (Array.isArray(value)) {\n\t\t\t\t\t\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tpack(writeResult)\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tlet currentTarget = target\n\t\t\t\t\t\t\t\tlet currentTargetView = targetView\n\t\t\t\t\t\t\t\tlet currentPosition = position\n\t\t\t\t\t\t\t\ttarget = null\n\t\t\t\t\t\t\t\tlet result\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tresult = extension.pack.call(this, value, (size) => {\n\t\t\t\t\t\t\t\t\t\t// restore target and use it\n\t\t\t\t\t\t\t\t\t\ttarget = currentTarget\n\t\t\t\t\t\t\t\t\t\tcurrentTarget = null\n\t\t\t\t\t\t\t\t\t\tposition += size\n\t\t\t\t\t\t\t\t\t\tif (position > safeEnd)\n\t\t\t\t\t\t\t\t\t\t\tmakeRoom(position)\n\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\ttarget, targetView, position: position - size\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}, pack)\n\t\t\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\t\t\t// restore current target information (unless already restored)\n\t\t\t\t\t\t\t\t\tif (currentTarget) {\n\t\t\t\t\t\t\t\t\t\ttarget = currentTarget\n\t\t\t\t\t\t\t\t\t\ttargetView = currentTargetView\n\t\t\t\t\t\t\t\t\t\tposition = currentPosition\n\t\t\t\t\t\t\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (result) {\n\t\t\t\t\t\t\t\t\tif (result.length + position > safeEnd)\n\t\t\t\t\t\t\t\t\t\tmakeRoom(result.length + position)\n\t\t\t\t\t\t\t\t\tposition = writeExtensionData(result, target, position, extension.type)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// check isArray after extensions, because extensions can extend Array\n\t\t\t\t\t\tif (Array.isArray(value)) {\n\t\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// use this as an alternate mechanism for expressing how to serialize\n\t\t\t\t\t\t\tif (value.toJSON) {\n\t\t\t\t\t\t\t\tconst json = value.toJSON()\n\t\t\t\t\t\t\t\t// if for some reason value.toJSON returns itself it'll loop forever\n\t\t\t\t\t\t\t\tif (json !== value)\n\t\t\t\t\t\t\t\t\treturn pack(json)\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// if there is a writeFunction, use it, otherwise just encode as undefined\n\t\t\t\t\t\t\tif (type === 'function')\n\t\t\t\t\t\t\t\treturn pack(this.writeFunction && this.writeFunction(value));\n\n\t\t\t\t\t\t\t// no extension found, write as plain object\n\t\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (type === 'boolean') {\n\t\t\t\ttarget[position++] = value ? 0xc3 : 0xc2\n\t\t\t} else if (type === 'bigint') {\n\t\t\t\tif (value < 0x8000000000000000 && value >= -0x8000000000000000) {\n\t\t\t\t\t// use a signed int as long as it fits\n\t\t\t\t\ttarget[position++] = 0xd3\n\t\t\t\t\ttargetView.setBigInt64(position, value)\n\t\t\t\t} else if (value < 0x10000000000000000 && value > 0) {\n\t\t\t\t\t// if we can fit an unsigned int, use that\n\t\t\t\t\ttarget[position++] = 0xcf\n\t\t\t\t\ttargetView.setBigUint64(position, value)\n\t\t\t\t} else {\n\t\t\t\t\t// overflow\n\t\t\t\t\tif (this.largeBigIntToFloat) {\n\t\t\t\t\t\ttarget[position++] = 0xcb\n\t\t\t\t\t\ttargetView.setFloat64(position, Number(value))\n\t\t\t\t\t} else if (this.largeBigIntToString) {\n\t\t\t\t\t\treturn pack(value.toString());\n\t\t\t\t\t} else if (this.useBigIntExtension || this.moreTypes) {\n\t\t\t\t\t\tlet empty = value < 0 ? BigInt(-1) : BigInt(0)\n\n\t\t\t\t\t\tlet array\n\t\t\t\t\t\tif (value >> BigInt(0x10000) === empty) {\n\t\t\t\t\t\t\tlet mask = BigInt(0x10000000000000000) - BigInt(1) // literal would overflow\n\t\t\t\t\t\t\tlet chunks = []\n\t\t\t\t\t\t\twhile (true) {\n\t\t\t\t\t\t\t\tchunks.push(value & mask)\n\t\t\t\t\t\t\t\tif ((value >> BigInt(63)) === empty) break\n\t\t\t\t\t\t\t\tvalue >>= BigInt(64)\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tarray = new Uint8Array(new BigUint64Array(chunks).buffer)\n\t\t\t\t\t\t\tarray.reverse()\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlet invert = value < 0\n\t\t\t\t\t\t\tlet string = (invert ? ~value : value).toString(16)\n\t\t\t\t\t\t\tif (string.length % 2) {\n\t\t\t\t\t\t\t\tstring = '0' + string\n\t\t\t\t\t\t\t} else if (parseInt(string.charAt(0), 16) >= 8) {\n\t\t\t\t\t\t\t\tstring = '00' + string\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (hasNodeBuffer) {\n\t\t\t\t\t\t\t\tarray = Buffer.from(string, 'hex')\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tarray = new Uint8Array(string.length / 2)\n\t\t\t\t\t\t\t\tfor (let i = 0; i < array.length; i++) {\n\t\t\t\t\t\t\t\t\tarray[i] = parseInt(string.slice(i * 2, i * 2 + 2), 16)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (invert) {\n\t\t\t\t\t\t\t\tfor (let i = 0; i < array.length; i++) array[i] = ~array[i]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (array.length + position > safeEnd)\n\t\t\t\t\t\t\tmakeRoom(array.length + position)\n\t\t\t\t\t\tposition = writeExtensionData(array, target, position, 0x42)\n\t\t\t\t\t\treturn\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new RangeError(value + ' was too large to fit in MessagePack 64-bit integer format, use' +\n\t\t\t\t\t\t\t' useBigIntExtension, or set largeBigIntToFloat to convert to float-64, or set' +\n\t\t\t\t\t\t\t' largeBigIntToString to convert to string')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tposition += 8\n\t\t\t} else if (type === 'undefined') {\n\t\t\t\tif (this.encodeUndefinedAsNil)\n\t\t\t\t\ttarget[position++] = 0xc0\n\t\t\t\telse {\n\t\t\t\t\ttarget[position++] = 0xd4 // a number of implementations use fixext1 with type 0, data 0 to denote undefined, so we follow suite\n\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthrow new Error('Unknown type: ' + type)\n\t\t\t}\n\t\t}\n\n\t\tconst writePlainObject = (this.variableMapSize || this.coercibleKeyAsNumber || this.skipValues) ? (object) => {\n\t\t\t// this method is slightly slower, but generates \"preferred serialization\" (optimally small for smaller objects)\n\t\t\tlet keys;\n\t\t\tif (this.skipValues) {\n\t\t\t\tkeys = [];\n\t\t\t\tfor (let key in object) {\n\t\t\t\t\tif ((typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) &&\n\t\t\t\t\t\t!this.skipValues.includes(object[key]))\n\t\t\t\t\t\tkeys.push(key);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tkeys = Object.keys(object)\n\t\t\t}\n\t\t\tlet length = keys.length\n\t\t\tif (length < 0x10) {\n\t\t\t\ttarget[position++] = 0x80 | length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xde\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xdf\n\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\tposition += 4\n\t\t\t}\n\t\t\tlet key\n\t\t\tif (this.coercibleKeyAsNumber) {\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tkey = keys[i]\n\t\t\t\t\tlet num = Number(key)\n\t\t\t\t\tpack(isNaN(num) ? key : num)\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\n\t\t\t} else {\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tpack(key = keys[i])\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t\t}\n\t\t} :\n\t\t(object) => {\n\t\t\ttarget[position++] = 0xde // always using map 16, so we can preallocate and set the length afterwards\n\t\t\tlet objectOffset = position - start\n\t\t\tposition += 2\n\t\t\tlet size = 0\n\t\t\tfor (let key in object) {\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tpack(key)\n\t\t\t\t\tpack(object[key])\n\t\t\t\t\tsize++\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (size > 0xffff) {\n\t\t\t\tthrow new Error('Object is too large to serialize with fast 16-bit map size,' +\n\t\t\t\t' use the \"variableMapSize\" option to serialize this object');\n\t\t\t}\n\t\t\ttarget[objectOffset++ + start] = size >> 8\n\t\t\ttarget[objectOffset + start] = size & 0xff\n\t\t}\n\n\t\tconst writeRecord = this.useRecords === false ? writePlainObject :\n\t\t(options.progressiveRecords && !useTwoByteRecords) ?  // this is about 2% faster for highly stable structures, since it only requires one for-in loop (but much more expensive when new structure needs to be written)\n\t\t(object) => {\n\t\t\tlet nextTransition, transition = structures.transitions || (structures.transitions = Object.create(null))\n\t\t\tlet objectOffset = position++ - start\n\t\t\tlet wroteKeys\n\t\t\tfor (let key in object) {\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\tif (nextTransition)\n\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\telse {\n\t\t\t\t\t\t// record doesn't exist, create full new record and insert it\n\t\t\t\t\t\tlet keys = Object.keys(object)\n\t\t\t\t\t\tlet lastTransition = transition\n\t\t\t\t\t\ttransition = structures.transitions\n\t\t\t\t\t\tlet newTransitions = 0\n\t\t\t\t\t\tfor (let i = 0, l = keys.length; i < l; i++) {\n\t\t\t\t\t\t\tlet key = keys[i]\n\t\t\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\t\t\tnewTransitions++\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (objectOffset + start + 1 == position) {\n\t\t\t\t\t\t\t// first key, so we don't need to insert, we can just write record directly\n\t\t\t\t\t\t\tposition--\n\t\t\t\t\t\t\tnewRecord(transition, keys, newTransitions)\n\t\t\t\t\t\t} else // otherwise we need to insert the record, moving existing data after the record\n\t\t\t\t\t\t\tinsertNewRecord(transition, keys, objectOffset, newTransitions)\n\t\t\t\t\t\twroteKeys = true\n\t\t\t\t\t\ttransition = lastTransition[key]\n\t\t\t\t\t}\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (!wroteKeys) {\n\t\t\t\tlet recordId = transition[RECORD_SYMBOL]\n\t\t\t\tif (recordId)\n\t\t\t\t\ttarget[objectOffset + start] = recordId\n\t\t\t\telse\n\t\t\t\t\tinsertNewRecord(transition, Object.keys(object), objectOffset, 0)\n\t\t\t}\n\t\t} :\n\t\t(object) => {\n\t\t\tlet nextTransition, transition = structures.transitions || (structures.transitions = Object.create(null))\n\t\t\tlet newTransitions = 0\n\t\t\tfor (let key in object) if (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\tnextTransition = transition[key]\n\t\t\t\tif (!nextTransition) {\n\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\tnewTransitions++\n\t\t\t\t}\n\t\t\t\ttransition = nextTransition\n\t\t\t}\n\t\t\tlet recordId = transition[RECORD_SYMBOL]\n\t\t\tif (recordId) {\n\t\t\t\tif (recordId >= 0x60 && useTwoByteRecords) {\n\t\t\t\t\ttarget[position++] = ((recordId -= 0x60) & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = recordId >> 5\n\t\t\t\t} else\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t} else {\n\t\t\t\tnewRecord(transition, transition.__keys__ || Object.keys(object), newTransitions)\n\t\t\t}\n\t\t\t// now write the values\n\t\t\tfor (let key in object)\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t}\n\n\t\t// create reference to useRecords if useRecords is a function\n\t\tconst checkUseRecords = typeof this.useRecords == 'function' && this.useRecords;\n\n\t\tconst writeObject = checkUseRecords ? (object) => {\n\t\t\tcheckUseRecords(object) ? writeRecord(object) : writePlainObject(object)\n\t\t} : writeRecord\n\n\t\tconst makeRoom = (end) => {\n\t\t\tlet newSize\n\t\t\tif (end > 0x1000000) {\n\t\t\t\t// special handling for really large buffers\n\t\t\t\tif ((end - start) > MAX_BUFFER_SIZE)\n\t\t\t\t\tthrow new Error('Packed buffer would be larger than maximum buffer size')\n\t\t\t\tnewSize = Math.min(MAX_BUFFER_SIZE,\n\t\t\t\t\tMath.round(Math.max((end - start) * (end > 0x4000000 ? 1.25 : 2), 0x400000) / 0x1000) * 0x1000)\n\t\t\t} else // faster handling for smaller buffers\n\t\t\t\tnewSize = ((Math.max((end - start) << 2, target.length - 1) >> 12) + 1) << 12\n\t\t\tlet newBuffer = new ByteArrayAllocate(newSize)\n\t\t\ttargetView = newBuffer.dataView || (newBuffer.dataView = new DataView(newBuffer.buffer, 0, newSize))\n\t\t\tend = Math.min(end, target.length)\n\t\t\tif (target.copy)\n\t\t\t\ttarget.copy(newBuffer, 0, start, end)\n\t\t\telse\n\t\t\t\tnewBuffer.set(target.slice(start, end))\n\t\t\tposition -= start\n\t\t\tstart = 0\n\t\t\tsafeEnd = newBuffer.length - 10\n\t\t\treturn target = newBuffer\n\t\t}\n\t\tconst newRecord = (transition, keys, newTransitions) => {\n\t\t\tlet recordId = structures.nextId\n\t\t\tif (!recordId)\n\t\t\t\trecordId = 0x40\n\t\t\tif (recordId < sharedLimitId && this.shouldShareStructure && !this.shouldShareStructure(keys)) {\n\t\t\t\trecordId = structures.nextOwnId\n\t\t\t\tif (!(recordId < maxStructureId))\n\t\t\t\t\trecordId = sharedLimitId\n\t\t\t\tstructures.nextOwnId = recordId + 1\n\t\t\t} else {\n\t\t\t\tif (recordId >= maxStructureId)// cycle back around\n\t\t\t\t\trecordId = sharedLimitId\n\t\t\t\tstructures.nextId = recordId + 1\n\t\t\t}\n\t\t\tlet highByte = keys.highByte = recordId >= 0x60 && useTwoByteRecords ? (recordId - 0x60) >> 5 : -1\n\t\t\ttransition[RECORD_SYMBOL] = recordId\n\t\t\ttransition.__keys__ = keys\n\t\t\tstructures[recordId - 0x40] = keys\n\n\t\t\tif (recordId < sharedLimitId) {\n\t\t\t\tkeys.isShared = true\n\t\t\t\tstructures.sharedLength = recordId - 0x3f\n\t\t\t\thasSharedUpdate = true\n\t\t\t\tif (highByte >= 0) {\n\t\t\t\t\ttarget[position++] = (recordId & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = highByte\n\t\t\t\t} else {\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (highByte >= 0) {\n\t\t\t\t\ttarget[position++] = 0xd5 // fixext 2\n\t\t\t\t\ttarget[position++] = 0x72 // \"r\" record defintion extension type\n\t\t\t\t\ttarget[position++] = (recordId & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = highByte\n\t\t\t\t} else {\n\t\t\t\t\ttarget[position++] = 0xd4 // fixext 1\n\t\t\t\t\ttarget[position++] = 0x72 // \"r\" record defintion extension type\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t\t}\n\n\t\t\t\tif (newTransitions)\n\t\t\t\t\ttransitionsCount += serializationsSinceTransitionRebuild * newTransitions\n\t\t\t\t// record the removal of the id, we can maintain our shared structure\n\t\t\t\tif (recordIdsToRemove.length >= maxOwnStructures)\n\t\t\t\t\trecordIdsToRemove.shift()[RECORD_SYMBOL] = 0 // we are cycling back through, and have to remove old ones\n\t\t\t\trecordIdsToRemove.push(transition)\n\t\t\t\tpack(keys)\n\t\t\t}\n\t\t}\n\t\tconst insertNewRecord = (transition, keys, insertionOffset, newTransitions) => {\n\t\t\tlet mainTarget = target\n\t\t\tlet mainPosition = position\n\t\t\tlet mainSafeEnd = safeEnd\n\t\t\tlet mainStart = start\n\t\t\ttarget = keysTarget\n\t\t\tposition = 0\n\t\t\tstart = 0\n\t\t\tif (!target)\n\t\t\t\tkeysTarget = target = new ByteArrayAllocate(8192)\n\t\t\tsafeEnd = target.length - 10\n\t\t\tnewRecord(transition, keys, newTransitions)\n\t\t\tkeysTarget = target\n\t\t\tlet keysPosition = position\n\t\t\ttarget = mainTarget\n\t\t\tposition = mainPosition\n\t\t\tsafeEnd = mainSafeEnd\n\t\t\tstart = mainStart\n\t\t\tif (keysPosition > 1) {\n\t\t\t\tlet newEnd = position + keysPosition - 1\n\t\t\t\tif (newEnd > safeEnd)\n\t\t\t\t\tmakeRoom(newEnd)\n\t\t\t\tlet insertionPosition = insertionOffset + start\n\t\t\t\ttarget.copyWithin(insertionPosition + keysPosition, insertionPosition + 1, position)\n\t\t\t\ttarget.set(keysTarget.slice(0, keysPosition), insertionPosition)\n\t\t\t\tposition = newEnd\n\t\t\t} else {\n\t\t\t\ttarget[insertionOffset + start] = keysTarget[0]\n\t\t\t}\n\t\t}\n\t\tconst writeStruct = (object) => {\n\t\t\tlet newPosition = writeStructSlots(object, target, start, position, structures, makeRoom, (value, newPosition, notifySharedUpdate) => {\n\t\t\t\tif (notifySharedUpdate)\n\t\t\t\t\treturn hasSharedUpdate = true;\n\t\t\t\tposition = newPosition;\n\t\t\t\tlet startTarget = target;\n\t\t\t\tpack(value);\n\t\t\t\tresetStructures();\n\t\t\t\tif (startTarget !== target) {\n\t\t\t\t\treturn { position, targetView, target }; // indicate the buffer was re-allocated\n\t\t\t\t}\n\t\t\t\treturn position;\n\t\t\t}, this);\n\t\t\tif (newPosition === 0) // bail and go to a msgpack object\n\t\t\t\treturn writeObject(object);\n\t\t\tposition = newPosition;\n\t\t}\n\t}\n\tuseBuffer(buffer) {\n\t\t// this means we are finished using our own buffer and we can write over it safely\n\t\ttarget = buffer\n\t\ttarget.dataView || (target.dataView = new DataView(target.buffer, target.byteOffset, target.byteLength))\n\t\ttargetView = target.dataView;\n\t\tposition = 0\n\t}\n\tset position (value) {\n\t\tposition = value;\n\t}\n\tget position() {\n\t\treturn position;\n\t}\n\tclearSharedData() {\n\t\tif (this.structures)\n\t\t\tthis.structures = []\n\t\tif (this.typedStructs)\n\t\t\tthis.typedStructs = []\n\t}\n}\n\nextensionClasses = [ Date, Set, Error, RegExp, ArrayBuffer, Object.getPrototypeOf(Uint8Array.prototype).constructor /*TypedArray*/, DataView, C1Type ]\nextensions = [{\n\tpack(date, allocateForWrite, pack) {\n\t\tlet seconds = date.getTime() / 1000\n\t\tif ((this.useTimestamp32 || date.getMilliseconds() === 0) && seconds >= 0 && seconds < 0x100000000) {\n\t\t\t// Timestamp 32\n\t\t\tlet { target, targetView, position} = allocateForWrite(6)\n\t\t\ttarget[position++] = 0xd6\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, seconds)\n\t\t} else if (seconds > 0 && seconds < 0x100000000) {\n\t\t\t// Timestamp 64\n\t\t\tlet { target, targetView, position} = allocateForWrite(10)\n\t\t\ttarget[position++] = 0xd7\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, date.getMilliseconds() * 4000000 + ((seconds / 1000 / 0x100000000) >> 0))\n\t\t\ttargetView.setUint32(position + 4, seconds)\n\t\t} else if (isNaN(seconds)) {\n\t\t\tif (this.onInvalidDate) {\n\t\t\t\tallocateForWrite(0)\n\t\t\t\treturn pack(this.onInvalidDate())\n\t\t\t}\n\t\t\t// Intentionally invalid timestamp\n\t\t\tlet { target, targetView, position} = allocateForWrite(3)\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0xff\n\t\t\ttarget[position++] = 0xff\n\t\t} else {\n\t\t\t// Timestamp 96\n\t\t\tlet { target, targetView, position} = allocateForWrite(15)\n\t\t\ttarget[position++] = 0xc7\n\t\t\ttarget[position++] = 12\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, date.getMilliseconds() * 1000000)\n\t\t\ttargetView.setBigInt64(position + 4, BigInt(Math.floor(seconds)))\n\t\t}\n\t}\n}, {\n\tpack(set, allocateForWrite, pack) {\n\t\tif (this.setAsEmptyObject) {\n\t\t\tallocateForWrite(0);\n\t\t\treturn pack({})\n\t\t}\n\t\tlet array = Array.from(set)\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x73 // 's' for Set\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack(array)\n\t}\n}, {\n\tpack(error, allocateForWrite, pack) {\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x65 // 'e' for error\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack([ error.name, error.message, error.cause ])\n\t}\n}, {\n\tpack(regex, allocateForWrite, pack) {\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x78 // 'x' for regeXp\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack([ regex.source, regex.flags ])\n\t}\n}, {\n\tpack(arrayBuffer, allocateForWrite) {\n\t\tif (this.moreTypes)\n\t\t\twriteExtBuffer(arrayBuffer, 0x10, allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(hasNodeBuffer ? Buffer.from(arrayBuffer) : new Uint8Array(arrayBuffer), allocateForWrite)\n\t}\n}, {\n\tpack(typedArray, allocateForWrite) {\n\t\tlet constructor = typedArray.constructor\n\t\tif (constructor !== ByteArray && this.moreTypes)\n\t\t\twriteExtBuffer(typedArray, typedArrays.indexOf(constructor.name), allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(typedArray, allocateForWrite)\n\t}\n}, {\n\tpack(arrayBuffer, allocateForWrite) {\n\t\tif (this.moreTypes)\n\t\t\twriteExtBuffer(arrayBuffer, 0x11, allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(hasNodeBuffer ? Buffer.from(arrayBuffer) : new Uint8Array(arrayBuffer), allocateForWrite)\n\t}\n}, {\n\tpack(c1, allocateForWrite) { // specific 0xC1 object\n\t\tlet { target, position} = allocateForWrite(1)\n\t\ttarget[position] = 0xc1\n\t}\n}]\n\nfunction writeExtBuffer(typedArray, type, allocateForWrite, encode) {\n\tlet length = typedArray.byteLength\n\tif (length + 1 < 0x100) {\n\t\tvar { target, position } = allocateForWrite(4 + length)\n\t\ttarget[position++] = 0xc7\n\t\ttarget[position++] = length + 1\n\t} else if (length + 1 < 0x10000) {\n\t\tvar { target, position } = allocateForWrite(5 + length)\n\t\ttarget[position++] = 0xc8\n\t\ttarget[position++] = (length + 1) >> 8\n\t\ttarget[position++] = (length + 1) & 0xff\n\t} else {\n\t\tvar { target, position, targetView } = allocateForWrite(7 + length)\n\t\ttarget[position++] = 0xc9\n\t\ttargetView.setUint32(position, length + 1) // plus one for the type byte\n\t\tposition += 4\n\t}\n\ttarget[position++] = 0x74 // \"t\" for typed array\n\ttarget[position++] = type\n\tif (!typedArray.buffer) typedArray = new Uint8Array(typedArray)\n\ttarget.set(new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength), position)\n}\nfunction writeBuffer(buffer, allocateForWrite) {\n\tlet length = buffer.byteLength\n\tvar target, position\n\tif (length < 0x100) {\n\t\tvar { target, position } = allocateForWrite(length + 2)\n\t\ttarget[position++] = 0xc4\n\t\ttarget[position++] = length\n\t} else if (length < 0x10000) {\n\t\tvar { target, position } = allocateForWrite(length + 3)\n\t\ttarget[position++] = 0xc5\n\t\ttarget[position++] = length >> 8\n\t\ttarget[position++] = length & 0xff\n\t} else {\n\t\tvar { target, position, targetView } = allocateForWrite(length + 5)\n\t\ttarget[position++] = 0xc6\n\t\ttargetView.setUint32(position, length)\n\t\tposition += 4\n\t}\n\ttarget.set(buffer, position)\n}\n\nfunction writeExtensionData(result, target, position, type) {\n\tlet length = result.length\n\tswitch (length) {\n\t\tcase 1:\n\t\t\ttarget[position++] = 0xd4\n\t\t\tbreak\n\t\tcase 2:\n\t\t\ttarget[position++] = 0xd5\n\t\t\tbreak\n\t\tcase 4:\n\t\t\ttarget[position++] = 0xd6\n\t\t\tbreak\n\t\tcase 8:\n\t\t\ttarget[position++] = 0xd7\n\t\t\tbreak\n\t\tcase 16:\n\t\t\ttarget[position++] = 0xd8\n\t\t\tbreak\n\t\tdefault:\n\t\t\tif (length < 0x100) {\n\t\t\t\ttarget[position++] = 0xc7\n\t\t\t\ttarget[position++] = length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xc8\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xc9\n\t\t\t\ttarget[position++] = length >> 24\n\t\t\t\ttarget[position++] = (length >> 16) & 0xff\n\t\t\t\ttarget[position++] = (length >> 8) & 0xff\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t}\n\t}\n\ttarget[position++] = type\n\ttarget.set(result, position)\n\tposition += length\n\treturn position\n}\n\nfunction insertIds(serialized, idsToInsert) {\n\t// insert the ids that need to be referenced for structured clones\n\tlet nextId\n\tlet distanceToMove = idsToInsert.length * 6\n\tlet lastEnd = serialized.length - distanceToMove\n\twhile (nextId = idsToInsert.pop()) {\n\t\tlet offset = nextId.offset\n\t\tlet id = nextId.id\n\t\tserialized.copyWithin(offset + distanceToMove, offset, lastEnd)\n\t\tdistanceToMove -= 6\n\t\tlet position = offset + distanceToMove\n\t\tserialized[position++] = 0xd6\n\t\tserialized[position++] = 0x69 // 'i'\n\t\tserialized[position++] = id >> 24\n\t\tserialized[position++] = (id >> 16) & 0xff\n\t\tserialized[position++] = (id >> 8) & 0xff\n\t\tserialized[position++] = id & 0xff\n\t\tlastEnd = offset\n\t}\n\treturn serialized\n}\n\nfunction writeBundles(start, pack, incrementPosition) {\n\tif (bundledStrings.length > 0) {\n\t\ttargetView.setUint32(bundledStrings.position + start, position + incrementPosition - bundledStrings.position - start)\n\t\tbundledStrings.stringsPosition = position - start;\n\t\tlet writeStrings = bundledStrings\n\t\tbundledStrings = null\n\t\tpack(writeStrings[0])\n\t\tpack(writeStrings[1])\n\t}\n}\n\nexport function addExtension(extension) {\n\tif (extension.Class) {\n\t\tif (!extension.pack && !extension.write)\n\t\t\tthrow new Error('Extension has no pack or write function')\n\t\tif (extension.pack && !extension.type)\n\t\t\tthrow new Error('Extension has no type (numeric code to identify the extension)')\n\t\textensionClasses.unshift(extension.Class)\n\t\textensions.unshift(extension)\n\t}\n\tunpackAddExtension(extension)\n}\nfunction prepareStructures(structures, packr) {\n\tstructures.isCompatible = (existingStructures) => {\n\t\tlet compatible = !existingStructures || ((packr.lastNamedStructuresLength || 0) === existingStructures.length)\n\t\tif (!compatible) // we want to merge these existing structures immediately since we already have it and we are in the right transaction\n\t\t\tpackr._mergeStructures(existingStructures);\n\t\treturn compatible;\n\t}\n\treturn structures\n}\nexport function setWriteStructSlots(writeSlots, makeStructures) {\n\twriteStructSlots = writeSlots;\n\tprepareStructures = makeStructures;\n}\n\nlet defaultPackr = new Packr({ useRecords: false })\nexport const pack = defaultPackr.pack\nexport const encode = defaultPackr.pack\nexport const Encoder = Packr\nexport { FLOAT32_OPTIONS } from './unpack.js'\nimport { FLOAT32_OPTIONS } from './unpack.js'\nexport const { NEVER, ALWAYS, DECIMAL_ROUND, DECIMAL_FIT } = FLOAT32_OPTIONS\nexport const REUSE_BUFFER_MODE = 512\nexport const RESET_BUFFER_MODE = 1024\nexport const RESERVE_START_SPACE = 2048\n", "import { Packr } from './pack.js'\nimport { Unpackr } from './unpack.js'\n\n/**\n * Given an Iterable first argument, returns an Iterable where each value is packed as a Buffer\n * If the argument is only Async Iterable, the return value will be an Async Iterable.\n * @param {Iterable|Iterator|AsyncIterable|AsyncIterator} objectIterator - iterable source, like a Readable object stream, an array, Set, or custom object\n * @param {options} [options] - msgpackr pack options\n * @returns {IterableIterator|Promise.<AsyncIterableIterator>}\n */\nexport function packIter (objectIterator, options = {}) {\n  if (!objectIterator || typeof objectIterator !== 'object') {\n    throw new Error('first argument must be an Iterable, Async Iterable, or a Promise for an Async Iterable')\n  } else if (typeof objectIterator[Symbol.iterator] === 'function') {\n    return packIterSync(objectIterator, options)\n  } else if (typeof objectIterator.then === 'function' || typeof objectIterator[Symbol.asyncIterator] === 'function') {\n    return packIterAsync(objectIterator, options)\n  } else {\n    throw new Error('first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a Promise')\n  }\n}\n\nfunction * packIterSync (objectIterator, options) {\n  const packr = new Packr(options)\n  for (const value of objectIterator) {\n    yield packr.pack(value)\n  }\n}\n\nasync function * packIterAsync (objectIterator, options) {\n  const packr = new Packr(options)\n  for await (const value of objectIterator) {\n    yield packr.pack(value)\n  }\n}\n\n/**\n * Given an Iterable/Iterator input which yields buffers, returns an IterableIterator which yields sync decoded objects\n * Or, given an Async Iterable/Iterator which yields promises resolving in buffers, returns an AsyncIterableIterator.\n * @param {Iterable|Iterator|AsyncIterable|AsyncIterableIterator} bufferIterator\n * @param {object} [options] - unpackr options\n * @returns {IterableIterator|Promise.<AsyncIterableIterator}\n */\nexport function unpackIter (bufferIterator, options = {}) {\n  if (!bufferIterator || typeof bufferIterator !== 'object') {\n    throw new Error('first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a promise')\n  }\n\n  const unpackr = new Unpackr(options)\n  let incomplete\n  const parser = (chunk) => {\n    let yields\n    // if there's incomplete data from previous chunk, concatinate and try again\n    if (incomplete) {\n      chunk = Buffer.concat([incomplete, chunk])\n      incomplete = undefined\n    }\n\n    try {\n      yields = unpackr.unpackMultiple(chunk)\n    } catch (err) {\n      if (err.incomplete) {\n        incomplete = chunk.slice(err.lastPosition)\n        yields = err.values\n      } else {\n        throw err\n      }\n    }\n    return yields\n  }\n\n  if (typeof bufferIterator[Symbol.iterator] === 'function') {\n    return (function * iter () {\n      for (const value of bufferIterator) {\n        yield * parser(value)\n      }\n    })()\n  } else if (typeof bufferIterator[Symbol.asyncIterator] === 'function') {\n    return (async function * iter () {\n      for await (const value of bufferIterator) {\n        yield * parser(value)\n      }\n    })()\n  }\n}\nexport const decodeIter = unpackIter\nexport const encodeIter = packIter", "export { Packr, Encoder, addExtension, pack, encode, NEVER, ALWAYS, DECIMAL_ROUND, DECIMAL_FIT, REUSE_BUFFER_MODE, RESET_BUFFER_MODE, RESERVE_START_SPACE } from './pack.js'\nexport { Unpackr, Decoder, C1, unpack, unpackMultiple, decode, FLOAT32_OPTIONS, clearSource, roundFloat32, isNativeAccelerationEnabled } from './unpack.js'\nexport { decodeIter, encodeIter } from './iterators.js'\nexport const useRecords = false\nexport const mapsAsObjects = true\n"], "names": ["decoder", "src", "srcEnd", "TextDecoder", "error", "currentStructures", "srcString", "bundledStrings", "referenceMap", "dataView", "position", "currentUnpackr", "srcStringStart", "srcStringEnd", "currentExtensions", "defaultOptions", "useRecords", "mapsAsObjects", "C1Type", "C1", "name", "BlockedFunction", "sequentialMode", "inlineObjectReadThreshold", "Infinity", "Unpackr", "constructor", "options", "undefined", "sequential", "trusted", "structures", "maxSharedStructures", "<PERSON><PERSON><PERSON><PERSON>", "length", "getStructures", "uninitialized", "int64AsNumber", "int64AsType", "Object", "assign", "this", "unpack", "source", "saveState", "clearSource", "prototype", "call", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "from", "Uint8Array", "end", "start", "DataView", "byteOffset", "byteLength", "Error", "checkedRead", "unpackMultiple", "for<PERSON>ach", "values", "lastPosition", "size", "value", "defaultUnpackr", "push", "_mergeStructures", "loadedStructures", "existingStructures", "isFrozen", "map", "structure", "slice", "i", "l", "isShared", "highByte", "id", "existing", "restoreStructures", "decode", "result", "randomAccessStructure", "read", "postBundlePosition", "jsonView", "JSON", "stringify", "_", "RangeError", "message", "startsWith", "incomplete", "token", "loadStructures", "createStructureReader", "object", "key", "read<PERSON><PERSON>", "Map", "set", "array", "Array", "freezeData", "freeze", "string", "shortStringInJS", "longStringInJS", "readFixedString", "position1", "position0", "readBin", "getUint16", "getUint32", "readExt", "getFloat32", "useFloat32", "multiplier", "mult10", "getFloat64", "getBigUint64", "toString", "BigInt", "Number", "getInt8", "getInt16", "getInt32", "getBigInt64", "recordDefinition", "extension", "noB<PERSON>er", "subarray", "readString8", "readString16", "readString32", "readArray", "readMap", "validName", "firstId", "readObject", "count", "test", "join", "createSecondByteReader", "read0", "readStringJS", "units", "byte1", "byte2", "byte3", "unit", "fromCharCode", "apply", "String", "bytes", "byte", "a", "b", "c", "d", "e", "f", "g", "h", "j", "k", "m", "n", "o", "readOnlyJSString", "copyBuffers", "type", "readPosition", "keyCache", "asSafeString", "chunk", "entry", "checkPosition", "property", "allowArraysInMapKeys", "isArray", "flat", "every", "item", "includes", "firstByte", "existingStructure", "data", "head<PERSON><PERSON><PERSON>", "head", "view", "out", "middle", "left", "right", "errors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReferenceError", "SyntaxError", "TypeError", "URIError", "AggregateError", "cause", "structuredClone", "target", "Set", "refEntry", "targetProperties", "used", "v", "entries", "add", "get", "typedArrays", "glbl", "globalThis", "window", "typeCode", "typedArrayName", "RegExp", "TEMP_BUNDLE", "callback", "savedSrcEnd", "savedPosition", "savedSrcStringStart", "savedSrcStringEnd", "savedSrcString", "savedReferenceMap", "savedBundledStrings", "savedSrc", "savedStructures", "savedStructuresContents", "savedPackr", "savedSequentialMode", "splice", "dataSize", "dataPosition", "Date", "Math", "floor", "Decoder", "FLOAT32_OPTIONS", "NEVER", "ALWAYS", "DECIMAL_ROUND", "DECIMAL_FIT", "textEncoder", "extensions", "extensionClasses", "f32Array", "Float32Array", "u8Array", "TextEncoder", "hasNodeBuffer", "ByteArrayAllocate", "allocUnsafeSlow", "ByteArray", "MAX_BUFFER_SIZE", "<PERSON><PERSON><PERSON><PERSON>", "targetView", "safeEnd", "hasNonLatin", "RECORD_SYMBOL", "Symbol", "<PERSON><PERSON>", "hasSharedUpdate", "super", "offset", "encodeUtf8", "utf8Write", "encodeInto", "written", "packr", "isSequential", "hasSharedStructures", "saveStructures", "moreTypes", "maxOwnStructures", "useTwoByteRecords", "sharedLimitId", "maxStructureId", "recordIdsToRemove", "transitionsCount", "serializationsSinceTransitionRebuild", "pack", "encode", "encodeOptions", "RESERVE_START_SPACE", "bundleStrings", "transitions", "create", "keys", "nextTransition", "transition", "lastNamedStructuresLength", "nextId", "encodingError", "writeStruct", "lastBundle", "writeBundles", "idsToInsert", "sort", "incrementPosition", "insertionPoint", "stringsPosition", "setUint32", "previous", "makeRoom", "serialized", "distanceToMove", "lastEnd", "pop", "copyWithin", "insertIds", "REUSE_BUFFER_MODE", "resetStructures", "<PERSON><PERSON><PERSON><PERSON>", "newSharedData", "isCompatible", "compatible", "prepareStructures", "RESET_BUFFER_MODE", "packArray", "headerSize", "str<PERSON><PERSON><PERSON>", "extStart", "maxBytes", "setUint16", "twoByte", "c1", "c2", "strPosition", "charCodeAt", "setInt16", "setInt32", "xShifted", "setFloat32", "setFloat64", "referee", "writeObject", "mapAsEmptyObject", "entryValue", "write", "writeResult", "currentTarget", "currentTargetView", "currentPosition", "writeExtensionData", "toJSON", "json", "writeFunction", "setBigInt64", "setBigUint64", "largeBigIntToFloat", "largeBigIntToString", "useBigIntExtension", "empty", "mask", "chunks", "BigUint64Array", "reverse", "invert", "parseInt", "char<PERSON>t", "encodeUndefinedAsNil", "writePlainObject", "variableMapSize", "coercibleKeyAsNumber", "<PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "num", "isNaN", "objectOffset", "writeRecord", "progressiveRecords", "<PERSON><PERSON><PERSON><PERSON>", "lastTransition", "newTransitions", "newRecord", "insertNewRecord", "recordId", "__keys__", "checkUseRecords", "newSize", "min", "round", "max", "new<PERSON>uffer", "copy", "shouldShareStructure", "nextOwnId", "shift", "insertionOffset", "mainTarget", "mainPosition", "mainSafeEnd", "mainStart", "keysPosition", "newEnd", "insertionPosition", "newPosition", "writeStructSlots", "notifySharedUpdate", "startTarget", "useBuffer", "clearSharedData", "typedStructs", "writeExtBuffer", "typedArray", "allocateForWrite", "writeBuffer", "writeStrings", "getPrototypeOf", "date", "seconds", "getTime", "useTimestamp32", "getMilliseconds", "onInvalidDate", "setAsEmptyObject", "regex", "flags", "arrayBuffer", "indexOf", "defaultPackr", "Encoder", "decodeIter", "bufferIterator", "unpackr", "parser", "yields", "concat", "err", "iterator", "asyncIterator", "encodeIter", "objectIterator", "packIterSync", "then", "async", "packIterAsync", "Class", "unshift", "unpackAddExtension", "float32Number"], "mappings": "gPAAA,IAAIA,EAIAC,EACAC,EAJJ,IACCF,EAAU,IAAIG,WACE,CAAf,MAAMC,GAAS,CAGjB,IAMIC,EACAC,EAGAC,EACAC,EAEAC,EAbAC,EAAW,EAKXC,EAAiB,CAAE,EAGnBC,EAAiB,EACjBC,EAAe,EAGfC,EAAoB,GAEpBC,EAAiB,CACpBC,YAAY,EACZC,eAAe,GAET,MAAMC,GACD,MAACC,EAAK,IAAID,EACtBC,EAAGC,KAAO,mBACV,IAGIC,EAHAC,GAAiB,EACjBC,EAA4B,EAIhC,IACC,IAAIF,EAAS,GAId,CAHE,MAAMjB,GAEPmB,EAA4BC,GAC7B,CAEO,MAAMC,EACZC,YAAYC,GACPA,KACwB,IAAvBA,EAAQX,iBAAkDY,IAA1BD,EAAQV,gBAC3CU,EAAQV,eAAgB,GACrBU,EAAQE,aAAkC,IAApBF,EAAQG,UACjCH,EAAQG,SAAU,EACbH,EAAQI,YAAoC,GAAtBJ,EAAQX,aAClCW,EAAQI,WAAa,GAChBJ,EAAQK,sBACZL,EAAQK,oBAAsB,KAG7BL,EAAQI,WACXJ,EAAQI,WAAWE,aAAeN,EAAQI,WAAWG,OAC7CP,EAAQQ,iBACfR,EAAQI,WAAa,IAAIK,eAAgB,EAC1CT,EAAQI,WAAWE,aAAe,GAE/BN,EAAQU,gBACXV,EAAQW,YAAc,WAGxBC,OAAOC,OAAOC,KAAMd,EACpB,CACDe,OAAOC,EAAQhB,GACd,GAAI1B,EAEH,OAAO2C,GAAU,KAChBC,IACOJ,KAAOA,KAAKC,OAAOC,EAAQhB,GAAWF,EAAQqB,UAAUJ,OAAOK,KAAKhC,EAAgB4B,EAAQhB,MAGhGgB,EAAOK,QAAUL,EAAOjB,cAAgBuB,cAC5CN,EAA2B,oBAAXO,OAAyBA,OAAOC,KAAKR,GAAU,IAAIS,WAAWT,IACxD,iBAAZhB,GACVzB,EAASyB,EAAQ0B,KAAOV,EAAOT,OAC/BxB,EAAWiB,EAAQ2B,OAAS,IAE5B5C,EAAW,EACXR,EAASyB,GAAW,EAAIA,EAAUgB,EAAOT,QAG1CrB,EAAe,EACfP,EAAY,KAEZC,EAAiB,KACjBN,EAAM0C,EAIN,IACClC,EAAWkC,EAAOlC,WAAakC,EAAOlC,SAAW,IAAI8C,SAASZ,EAAOK,OAAQL,EAAOa,WAAYb,EAAOc,YAOvG,CANC,MAAMrD,GAGP,GADAH,EAAM,KACF0C,aAAkBS,WACrB,MAAMhD,EACP,MAAM,IAAIsD,MAAM,oDAAuDf,GAA2B,iBAAVA,EAAsBA,EAAOjB,YAAYN,YAAcuB,GAC/I,CACD,GAAIF,gBAAgBhB,EAAS,CAE5B,GADAd,EAAiB8B,KACbA,KAAKV,WAER,OADA1B,EAAoBoC,KAAKV,WAClB4B,EAAYhC,KACRtB,GAAqBA,EAAkB6B,OAAS,KAC3D7B,EAAoB,GAExB,MACGM,EAAiBI,IACZV,GAAqBA,EAAkB6B,OAAS,KACpD7B,EAAoB,IAEtB,OAAOsD,EAAYhC,EACnB,CACDiC,eAAejB,EAAQkB,GACtB,IAAIC,EAAQC,EAAe,EAC3B,IACCzC,GAAiB,EACjB,IAAI0C,EAAOrB,EAAOT,OACd+B,EAAQxB,KAAOA,KAAKC,OAAOC,EAAQqB,GAAQE,EAAexB,OAAOC,EAAQqB,GAC7E,IAAIH,EASC,CAEJ,IADAC,EAAS,CAAEG,GACLvD,EAAWsD,GAChBD,EAAerD,EACfoD,EAAOK,KAAKR,KAEb,OAAOG,CACP,CAfA,IAA+C,IAA3CD,EAAQI,EAAOF,EAAcrD,GAAqB,OACtD,KAAMA,EAAWsD,GAEhB,GADAD,EAAerD,GACwC,IAAnDmD,EAAQF,IAAeI,EAAcrD,GACxC,MAmBH,CAPC,MAAMN,GAGP,MAFAA,EAAM2D,aAAeA,EACrB3D,EAAM0D,OAASA,EACT1D,CACT,CAAY,QACTkB,GAAiB,EACjBuB,GACA,CACD,CACDuB,iBAAiBC,EAAkBC,GAGlCD,EAAmBA,GAAoB,GACnC9B,OAAOgC,SAASF,KACnBA,EAAmBA,EAAiBG,KAAIC,GAAaA,EAAUC,MAAM,MACtE,IAAK,IAAIC,EAAI,EAAGC,EAAIP,EAAiBnC,OAAQyC,EAAIC,EAAGD,IAAK,CACxD,IAAIF,EAAYJ,EAAiBM,GAC7BF,IACHA,EAAUI,UAAW,EACjBF,GAAK,KACRF,EAAUK,SAAYH,EAAI,IAAO,GAEnC,CACDN,EAAiBpC,aAAeoC,EAAiBnC,OACjD,IAAK,IAAI6C,KAAMT,GAAsB,GACpC,GAAIS,GAAM,EAAG,CACZ,IAAIN,EAAYJ,EAAiBU,GAC7BC,EAAWV,EAAmBS,GAC9BC,IACCP,KACFJ,EAAiBY,oBAAsBZ,EAAiBY,kBAAoB,KAAKF,GAAMN,GACzFJ,EAAiBU,GAAMC,EAExB,CAEF,OAAOvC,KAAKV,WAAasC,CACzB,CACDa,OAAOvC,EAAQhB,GACd,OAAOc,KAAKC,OAAOC,EAAQhB,EAC3B,EAKK,SAASgC,EAAYhC,GAC3B,IACC,IAAKhB,EAAemB,UAAYR,EAAgB,CAC/C,IAAIW,EAAe5B,EAAkB4B,cAAgB,EACjDA,EAAe5B,EAAkB6B,SACpC7B,EAAkB6B,OAASD,EAC5B,CACD,IAAIkD,EAkBJ,GAjBIxE,EAAeyE,uBAAyBnF,EAAIS,GAAY,IAAQT,EAAIS,GAOvEyE,EAASE,IACN9E,IACHG,EAAWH,EAAe+E,mBAC1B/E,EAAiB,MAEde,IAGHjB,EAAkB4E,kBAAoB,MAEnCvE,GAAYR,EAEXG,GAAqBA,EAAkB4E,mBAC1CA,IACD5E,EAAoB,KACpBJ,EAAM,KACFO,IACHA,EAAe,UACV,IAAIE,EAAWR,EAErB,MAAM,IAAIwD,MAAM,sCACV,IAAKpC,EAAgB,CAC3B,IAAIiE,EACJ,IACCA,EAAWC,KAAKC,UAAUN,GAAQ,CAACO,EAAGzB,IAA2B,iBAAVA,EAAqB,GAAGA,KAAWA,IAAOS,MAAM,EAAG,IAG1G,CAFC,MAAMtE,GACPmF,EAAW,4BAA8BnF,EAAQ,GACjD,CACD,MAAM,IAAIsD,MAAM,4CAA8C6B,EAC9D,EAED,OAAOJ,CASP,CARC,MAAM/E,GAOP,MANIC,GAAqBA,EAAkB4E,mBAC1CA,IACDpC,KACIzC,aAAiBuF,YAAcvF,EAAMwF,QAAQC,WAAW,6BAA+BnF,EAAWR,KACrGE,EAAM0F,YAAa,GAEd1F,CACN,CACF,CAEA,SAAS6E,IACR,IAAK,IAAIF,KAAM1E,EAAkB4E,kBAChC5E,EAAkB0E,GAAM1E,EAAkB4E,kBAAkBF,GAE7D1E,EAAkB4E,kBAAoB,IACvC,CAEO,SAASI,IACf,IAAIU,EAAQ9F,EAAIS,KAChB,GAAIqF,EAAQ,IAAM,CACjB,GAAIA,EAAQ,IAAM,CACjB,GAAIA,EAAQ,GACX,OAAOA,EACH,CACJ,IAAItB,EAAYpE,EAA0B,GAAR0F,IACjCpF,EAAewB,eAAiB6D,IAAyB,GAARD,GAClD,OAAItB,GACEA,EAAUY,OACdZ,EAAUY,KAAOY,EAAsBxB,EAAmB,GAARsB,IAE5CtB,EAAUY,QAEVU,CACR,CACJ,CAAS,GAAIA,EAAQ,IAAM,CAGxB,GADAA,GAAS,IACLpF,EAAeM,cAAe,CACjC,IAAIiF,EAAS,CAAE,EACf,IAAK,IAAIvB,EAAI,EAAGA,EAAIoB,EAAOpB,IAAK,CAC/B,IAAIwB,EAAMC,IACE,cAARD,IACHA,EAAM,YACPD,EAAOC,GAAOd,GACd,CACD,OAAOa,CACX,CAAU,CACN,IAAI1B,EAAM,IAAI6B,IACd,IAAK,IAAI1B,EAAI,EAAGA,EAAIoB,EAAOpB,IAC1BH,EAAI8B,IAAIjB,IAAQA,KAEjB,OAAOb,CACP,CACJ,CAAS,CACNuB,GAAS,IACT,IAAIQ,EAAQ,IAAIC,MAAMT,GACtB,IAAK,IAAIpB,EAAI,EAAGA,EAAIoB,EAAOpB,IAC1B4B,EAAM5B,GAAKU,IAEZ,OAAI1E,EAAe8F,WACXlE,OAAOmE,OAAOH,GACfA,CACP,CACH,CAAQ,GAAIR,EAAQ,IAAM,CAExB,IAAI7D,EAAS6D,EAAQ,IACrB,GAAIlF,GAAgBH,EACnB,OAAOJ,EAAUoE,MAAMhE,EAAWE,GAAiBF,GAAYwB,GAAUtB,GAE1E,GAAoB,GAAhBC,GAAqBX,EAAS,IAAK,CAEtC,IAAIyG,EAASzE,EAAS,GAAK0E,EAAgB1E,GAAU2E,EAAe3E,GACpE,GAAc,MAAVyE,EACH,OAAOA,CACR,CACD,OAAOG,EAAgB5E,EACzB,CAAQ,CACN,IAAI+B,EACJ,OAAQ8B,GACP,KAAK,IAAM,OAAO,KAClB,KAAK,IACJ,OAAIxF,GACH0D,EAAQoB,IACJpB,EAAQ,EACJ1D,EAAe,GAAGmE,MAAMnE,EAAewG,UAAWxG,EAAewG,WAAa9C,GAE9E1D,EAAe,GAAGmE,MAAMnE,EAAeyG,UAAWzG,EAAeyG,WAAa/C,IAEhF9C,EACR,KAAK,IAAM,OAAO,EAClB,KAAK,IAAM,OAAO,EAClB,KAAK,IAGJ,GADA8C,EAAQhE,EAAIS,UACEkB,IAAVqC,EACH,MAAM,IAAIP,MAAM,4BACjB,OAAOuD,EAAQhD,GAChB,KAAK,IAIJ,OAFAA,EAAQxD,EAASyG,UAAUxG,GAC3BA,GAAY,EACLuG,EAAQhD,GAChB,KAAK,IAIJ,OAFAA,EAAQxD,EAAS0G,UAAUzG,GAC3BA,GAAY,EACLuG,EAAQhD,GAChB,KAAK,IAEJ,OAAOmD,EAAQnH,EAAIS,MACpB,KAAK,IAIJ,OAFAuD,EAAQxD,EAASyG,UAAUxG,GAC3BA,GAAY,EACL0G,EAAQnD,GAChB,KAAK,IAIJ,OAFAA,EAAQxD,EAAS0G,UAAUzG,GAC3BA,GAAY,EACL0G,EAAQnD,GAChB,KAAK,IAEJ,GADAA,EAAQxD,EAAS4G,WAAW3G,GACxBC,EAAe2G,WAAa,EAAG,CAElC,IAAIC,EAAaC,GAAyB,IAAhBvH,EAAIS,KAAqB,EAAMT,EAAIS,EAAW,IAAM,GAE9E,OADAA,GAAY,GACH6G,EAAatD,GAASA,EAAQ,EAAI,IAAO,KAAS,GAAKsD,CAChE,CAED,OADA7G,GAAY,EACLuD,EACR,KAAK,IAGJ,OAFAA,EAAQxD,EAASgH,WAAW/G,GAC5BA,GAAY,EACLuD,EAER,KAAK,IACJ,OAAOhE,EAAIS,KACZ,KAAK,IAGJ,OAFAuD,EAAQxD,EAASyG,UAAUxG,GAC3BA,GAAY,EACLuD,EACR,KAAK,IAGJ,OAFAA,EAAQxD,EAAS0G,UAAUzG,GAC3BA,GAAY,EACLuD,EACR,KAAK,IAYJ,MAXmC,WAA/BtD,EAAe2B,aAClB2B,EAAuC,WAA/BxD,EAAS0G,UAAUzG,GAC3BuD,GAASxD,EAAS0G,UAAUzG,EAAW,IACE,WAA/BC,EAAe2B,YACzB2B,EAAQxD,EAASiH,aAAahH,GAAUiH,WACC,SAA/BhH,EAAe2B,aACzB2B,EAAQxD,EAASiH,aAAahH,GAC1BuD,GAAO2D,OAAO,IAAIA,OAAO,MAAK3D,EAAM4D,OAAO5D,KAE/CA,EAAQxD,EAASiH,aAAahH,GAC/BA,GAAY,EACLuD,EAGR,KAAK,IACJ,OAAOxD,EAASqH,QAAQpH,KACzB,KAAK,IAGJ,OAFAuD,EAAQxD,EAASsH,SAASrH,GAC1BA,GAAY,EACLuD,EACR,KAAK,IAGJ,OAFAA,EAAQxD,EAASuH,SAAStH,GAC1BA,GAAY,EACLuD,EACR,KAAK,IAYJ,MAXmC,WAA/BtD,EAAe2B,aAClB2B,EAAsC,WAA9BxD,EAASuH,SAAStH,GAC1BuD,GAASxD,EAAS0G,UAAUzG,EAAW,IACE,WAA/BC,EAAe2B,YACzB2B,EAAQxD,EAASwH,YAAYvH,GAAUiH,WACE,SAA/BhH,EAAe2B,aACzB2B,EAAQxD,EAASwH,YAAYvH,GACzBuD,GAAO2D,QAAQ,IAAIA,OAAO,KAAK3D,GAAO2D,OAAO,IAAIA,OAAO,MAAK3D,EAAM4D,OAAO5D,KAE9EA,EAAQxD,EAASwH,YAAYvH,GAC9BA,GAAY,EACLuD,EAER,KAAK,IAGJ,GADAA,EAAQhE,EAAIS,KACC,KAATuD,EACH,OAAOiE,EAAmC,GAAlBjI,EAAIS,MACtB,CACN,IAAIyH,EAAYrH,EAAkBmD,GAClC,GAAIkE,EACH,OAAIA,EAAU9C,MACb3E,IACOyH,EAAU9C,KAAKA,MACZ8C,EAAUC,UACpB1H,IACOyH,KAEAA,EAAUlI,EAAIoI,SAAS3H,IAAYA,IAE3C,MAAM,IAAIgD,MAAM,qBAAuBO,EACxC,CACF,KAAK,IAGJ,OADAA,EAAQhE,EAAIS,GACC,KAATuD,GACHvD,IACOwH,EAAmC,GAAlBjI,EAAIS,KAAoBT,EAAIS,OAE7C0G,EAAQ,GACjB,KAAK,IAEJ,OAAOA,EAAQ,GAChB,KAAK,IAEJ,OAAOA,EAAQ,GAChB,KAAK,IAEJ,OAAOA,EAAQ,IAChB,KAAK,IAGJ,OADAnD,EAAQhE,EAAIS,KACRG,GAAgBH,EACZJ,EAAUoE,MAAMhE,EAAWE,GAAiBF,GAAYuD,GAASrD,GAElE0H,EAAYrE,GACpB,KAAK,IAIJ,OAFAA,EAAQxD,EAASyG,UAAUxG,GAEvBG,IADJH,GAAY,GAEJJ,EAAUoE,MAAMhE,EAAWE,GAAiBF,GAAYuD,GAASrD,GAElE2H,EAAatE,GACrB,KAAK,IAIJ,OAFAA,EAAQxD,EAAS0G,UAAUzG,GAEvBG,IADJH,GAAY,GAEJJ,EAAUoE,MAAMhE,EAAWE,GAAiBF,GAAYuD,GAASrD,GAElE4H,EAAavE,GACrB,KAAK,IAIJ,OAFAA,EAAQxD,EAASyG,UAAUxG,GAC3BA,GAAY,EACL+H,EAAUxE,GAClB,KAAK,IAIJ,OAFAA,EAAQxD,EAAS0G,UAAUzG,GAC3BA,GAAY,EACL+H,EAAUxE,GAClB,KAAK,IAIJ,OAFAA,EAAQxD,EAASyG,UAAUxG,GAC3BA,GAAY,EACLgI,EAAQzE,GAChB,KAAK,IAIJ,OAFAA,EAAQxD,EAAS0G,UAAUzG,GAC3BA,GAAY,EACLgI,EAAQzE,GAChB,QACC,GAAI8B,GAAS,IACZ,OAAOA,EAAQ,IAChB,QAAcnE,IAAVmE,EAAqB,CACxB,IAAI3F,EAAQ,IAAIsD,MAAM,sCAEtB,MADAtD,EAAM0F,YAAa,EACb1F,CACN,CACD,MAAM,IAAIsD,MAAM,6BAA+BqC,GAGjD,CACF,CACA,MAAM4C,EAAY,4BAClB,SAAS1C,EAAsBxB,EAAWmE,GACzC,SAASC,IAER,GAAIA,EAAWC,QAAUvH,EAA2B,CACnD,IAAIsH,EAAapE,EAAUY,KAAO,IAAKhE,EAAS,IAAK,6BAA+BV,EAAe8F,WAAa,gBAAkB,IACjI,KAAOhC,EAAUD,KAAI2B,GAAe,cAARA,EAAsB,eAAiBwC,EAAUI,KAAK5C,GAAOA,EAAM,OAAU,IAAMX,KAAKC,UAAUU,GAAO,UAAU6C,KAAK,KAAO,MAD1H,CACkI3D,GAGpK,OAF2B,IAAvBZ,EAAUK,WACbL,EAAUY,KAAO4D,EAAuBL,EAASnE,EAAUY,OACrDwD,GACP,CACD,IAAI3C,EAAS,CAAE,EACf,IAAK,IAAIvB,EAAI,EAAGC,EAAIH,EAAUvC,OAAQyC,EAAIC,EAAGD,IAAK,CACjD,IAAIwB,EAAM1B,EAAUE,GACR,cAARwB,IACHA,EAAM,YACPD,EAAOC,GAAOd,GACd,CACD,OAAI1E,EAAe8F,WACXlE,OAAOmE,OAAOR,GACfA,CACP,CAED,OADA2C,EAAWC,MAAQ,EACQ,IAAvBrE,EAAUK,SACNmE,EAAuBL,EAASC,GAEjCA,CACR,CAEA,MAAMI,EAAyB,CAACL,EAASM,IACjC,WACN,IAAIpE,EAAW7E,EAAIS,KACnB,GAAiB,IAAboE,EACH,OAAOoE,IACR,IAAInE,EAAK6D,EAAU,KAAOA,GAAW9D,GAAY,IAAM8D,GAAW9D,GAAY,GAC1EL,EAAYpE,EAAkB0E,IAAOiB,IAAiBjB,GAC1D,IAAKN,EACJ,MAAM,IAAIf,MAAM,gCAAkCqB,GAInD,OAFKN,EAAUY,OACdZ,EAAUY,KAAOY,EAAsBxB,EAAWmE,IAC5CnE,EAAUY,MACjB,EAGK,SAASW,IACf,IAAI3B,EAAmBzB,GAAU,KAEhC3C,EAAM,KACCU,EAAewB,mBAEvB,OAAO9B,EAAoBM,EAAeyD,iBAAiBC,EAAkBhE,EAC9E,CAEA,IAAIyG,EAAkBqC,EAClBb,EAAca,EACdZ,EAAeY,EACfX,EAAeW,EA0CnB,SAASA,EAAajH,GACrB,IAAIiD,EACJ,GAAIjD,EAAS,KACRiD,EAASyB,EAAgB1E,IAC5B,OAAOiD,EAET,GAAIjD,EAAS,IAAMlC,EAClB,OAAOA,EAAQkF,OAAOjF,EAAIoI,SAAS3H,EAAUA,GAAYwB,IAC1D,MAAMmB,EAAM3C,EAAWwB,EACjBkH,EAAQ,GAEd,IADAjE,EAAS,GACFzE,EAAW2C,GAAK,CACtB,MAAMgG,EAAQpJ,EAAIS,KAClB,GAAuB,IAAV,IAAR2I,GAEJD,EAAMjF,KAAKkF,QACL,GAAuB,MAAV,IAARA,GAAwB,CAEnC,MAAMC,EAA0B,GAAlBrJ,EAAIS,KAClB0I,EAAMjF,MAAe,GAARkF,IAAiB,EAAKC,EACnC,MAAM,GAAuB,MAAV,IAARD,GAAwB,CAEnC,MAAMC,EAA0B,GAAlBrJ,EAAIS,KACZ6I,EAA0B,GAAlBtJ,EAAIS,KAClB0I,EAAMjF,MAAe,GAARkF,IAAiB,GAAOC,GAAS,EAAKC,EACnD,MAAM,GAAuB,MAAV,IAARF,GAAwB,CAKnC,IAAIG,GAAiB,EAARH,IAAiB,IAHE,GAAlBpJ,EAAIS,OAG8B,IAFhB,GAAlBT,EAAIS,OAEgD,EADlC,GAAlBT,EAAIS,KAEd8I,EAAO,QACVA,GAAQ,MACRJ,EAAMjF,KAAOqF,IAAS,GAAM,KAAS,OACrCA,EAAO,MAAiB,KAAPA,GAElBJ,EAAMjF,KAAKqF,EACd,MACGJ,EAAMjF,KAAKkF,GAGRD,EAAMlH,QAAU,OACnBiD,GAAUsE,EAAaC,MAAMC,OAAQP,GACrCA,EAAMlH,OAAS,EAEhB,CAMD,OAJIkH,EAAMlH,OAAS,IAClBiD,GAAUsE,EAAaC,MAAMC,OAAQP,IAG/BjE,CACR,CAYA,SAASsD,EAAUvG,GAClB,IAAIqE,EAAQ,IAAIC,MAAMtE,GACtB,IAAK,IAAIyC,EAAI,EAAGA,EAAIzC,EAAQyC,IAC3B4B,EAAM5B,GAAKU,IAEZ,OAAI1E,EAAe8F,WACXlE,OAAOmE,OAAOH,GACfA,CACR,CAEA,SAASmC,EAAQxG,GAChB,GAAIvB,EAAeM,cAAe,CACjC,IAAIiF,EAAS,CAAE,EACf,IAAK,IAAIvB,EAAI,EAAGA,EAAIzC,EAAQyC,IAAK,CAChC,IAAIwB,EAAMC,IACE,cAARD,IACHA,EAAM,YACPD,EAAOC,GAAOd,GACd,CACD,OAAOa,CACT,CAAQ,CACN,IAAI1B,EAAM,IAAI6B,IACd,IAAK,IAAI1B,EAAI,EAAGA,EAAIzC,EAAQyC,IAC3BH,EAAI8B,IAAIjB,IAAQA,KAEjB,OAAOb,CACP,CACF,CAEA,IAAIiF,EAAeE,OAAOF,aAC1B,SAAS5C,EAAe3E,GACvB,IAAIoB,EAAQ5C,EACRkJ,EAAQ,IAAIpD,MAAMtE,GACtB,IAAK,IAAIyC,EAAI,EAAGA,EAAIzC,EAAQyC,IAAK,CAChC,MAAMkF,EAAO5J,EAAIS,KACjB,IAAY,IAAPmJ,GAAe,EAElB,YADAnJ,EAAW4C,GAGZsG,EAAMjF,GAAKkF,CACX,CACD,OAAOJ,EAAaC,MAAMC,OAAQC,EACpC,CACA,SAAShD,EAAgB1E,GACxB,GAAIA,EAAS,EAAG,CACf,GAAIA,EAAS,EAAG,CACf,GAAe,IAAXA,EACH,MAAO,GACH,CACJ,IAAI4H,EAAI7J,EAAIS,KACZ,OAAS,IAAJoJ,GAAY,OAChBpJ,GAAY,GAGN+I,EAAaK,EACpB,CACJ,CAAS,CACN,IAAIA,EAAI7J,EAAIS,KACRqJ,EAAI9J,EAAIS,KACZ,IAAS,IAAJoJ,GAAY,IAAU,IAAJC,GAAY,EAElC,YADArJ,GAAY,GAGb,GAAIwB,EAAS,EACZ,OAAOuH,EAAaK,EAAGC,GACxB,IAAIC,EAAI/J,EAAIS,KACZ,OAAS,IAAJsJ,GAAY,OAChBtJ,GAAY,GAGN+I,EAAaK,EAAGC,EAAGC,EAC1B,CACH,CAAQ,CACN,IAAIF,EAAI7J,EAAIS,KACRqJ,EAAI9J,EAAIS,KACRsJ,EAAI/J,EAAIS,KACRuJ,EAAIhK,EAAIS,KACZ,IAAS,IAAJoJ,GAAY,IAAU,IAAJC,GAAY,IAAU,IAAJC,GAAY,IAAU,IAAJC,GAAY,EAEtE,YADAvJ,GAAY,GAGb,GAAIwB,EAAS,EAAG,CACf,GAAe,IAAXA,EACH,OAAOuH,EAAaK,EAAGC,EAAGC,EAAGC,GACzB,CACJ,IAAIC,EAAIjK,EAAIS,KACZ,OAAS,IAAJwJ,GAAY,OAChBxJ,GAAY,GAGN+I,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAChC,CACJ,CAAS,GAAIhI,EAAS,EAAG,CACtB,IAAIgI,EAAIjK,EAAIS,KACRyJ,EAAIlK,EAAIS,KACZ,IAAS,IAAJwJ,GAAY,IAAU,IAAJC,GAAY,EAElC,YADAzJ,GAAY,GAGb,GAAIwB,EAAS,EACZ,OAAOuH,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GACpC,IAAIC,EAAInK,EAAIS,KACZ,OAAS,IAAJ0J,GAAY,OAChB1J,GAAY,GAGN+I,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EACzC,CAAS,CACN,IAAIF,EAAIjK,EAAIS,KACRyJ,EAAIlK,EAAIS,KACR0J,EAAInK,EAAIS,KACR2J,EAAIpK,EAAIS,KACZ,IAAS,IAAJwJ,GAAY,IAAU,IAAJC,GAAY,IAAU,IAAJC,GAAY,IAAU,IAAJC,GAAY,EAEtE,YADA3J,GAAY,GAGb,GAAIwB,EAAS,GAAI,CAChB,GAAe,IAAXA,EACH,OAAOuH,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GACrC,CACJ,IAAI1F,EAAI1E,EAAIS,KACZ,OAAS,IAAJiE,GAAY,OAChBjE,GAAY,GAGN+I,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAG1F,EAC5C,CACL,CAAU,GAAIzC,EAAS,GAAI,CACvB,IAAIyC,EAAI1E,EAAIS,KACR4J,EAAIrK,EAAIS,KACZ,IAAS,IAAJiE,GAAY,IAAU,IAAJ2F,GAAY,EAElC,YADA5J,GAAY,IAGb,GAAIwB,EAAS,GACZ,OAAOuH,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAG1F,EAAG2F,GAChD,IAAIC,EAAItK,EAAIS,KACZ,OAAS,IAAJ6J,GAAY,OAChB7J,GAAY,IAGN+I,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAG1F,EAAG2F,EAAGC,EACtD,CAAU,CACN,IAAI5F,EAAI1E,EAAIS,KACR4J,EAAIrK,EAAIS,KACR6J,EAAItK,EAAIS,KACRkE,EAAI3E,EAAIS,KACZ,IAAS,IAAJiE,GAAY,IAAU,IAAJ2F,GAAY,IAAU,IAAJC,GAAY,IAAU,IAAJ3F,GAAY,EAEtE,YADAlE,GAAY,IAGb,GAAIwB,EAAS,GAAI,CAChB,GAAe,KAAXA,EACH,OAAOuH,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAG1F,EAAG2F,EAAGC,EAAG3F,GACjD,CACJ,IAAI4F,EAAIvK,EAAIS,KACZ,OAAS,IAAJ8J,GAAY,OAChB9J,GAAY,IAGN+I,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAG1F,EAAG2F,EAAGC,EAAG3F,EAAG4F,EACxD,CACN,CAAW,CACN,IAAIA,EAAIvK,EAAIS,KACR+J,EAAIxK,EAAIS,KACZ,IAAS,IAAJ8J,GAAY,IAAU,IAAJC,GAAY,EAElC,YADA/J,GAAY,IAGb,GAAIwB,EAAS,GACZ,OAAOuH,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAG1F,EAAG2F,EAAGC,EAAG3F,EAAG4F,EAAGC,GAC5D,IAAIC,EAAIzK,EAAIS,KACZ,OAAS,IAAJgK,GAAY,OAChBhK,GAAY,IAGN+I,EAAaK,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAG1F,EAAG2F,EAAGC,EAAG3F,EAAG4F,EAAGC,EAAGC,EAC9D,CACD,CACD,CACD,CACF,CAEA,SAASC,IACR,IACIzI,EADA6D,EAAQ9F,EAAIS,KAEhB,GAAIqF,EAAQ,IAEX7D,EAAS6D,EAAQ,SAEjB,OAAOA,GACN,KAAK,IAEJ7D,EAASjC,EAAIS,KACb,MACD,KAAK,IAEJwB,EAASzB,EAASyG,UAAUxG,GAC5BA,GAAY,EACZ,MACD,KAAK,IAEJwB,EAASzB,EAAS0G,UAAUzG,GAC5BA,GAAY,EACZ,MACD,QACC,MAAM,IAAIgD,MAAM,mBAGnB,OAAOyF,EAAajH,EACrB,CAGA,SAAS+E,EAAQ/E,GAChB,OAAOvB,EAAeiK,YAErBxH,WAAWN,UAAU4B,MAAM3B,KAAK9C,EAAKS,EAAUA,GAAYwB,GAC3DjC,EAAIoI,SAAS3H,EAAUA,GAAYwB,EACrC,CACA,SAASkF,EAAQlF,GAChB,IAAI2I,EAAO5K,EAAIS,KACf,GAAII,EAAkB+J,GAAO,CAC5B,IAAIxH,EACJ,OAAOvC,EAAkB+J,GAAM5K,EAAIoI,SAAS3H,EAAU2C,EAAO3C,GAAYwB,IAAW4I,IACnFpK,EAAWoK,EACX,IACC,OAAOzF,GAGP,CAFS,QACT3E,EAAW2C,CACX,IAEF,CAEA,MAAM,IAAIK,MAAM,0BAA4BmH,EAC9C,CAEA,IAAIE,EAAW,IAAIvE,MAAM,MACzB,SAASJ,IACR,IAAIlE,EAASjC,EAAIS,KACjB,KAAIwB,GAAU,KAAQA,EAAS,KAS9B,OADAxB,IACOsK,EAAa3F,KANpB,GADAnD,GAAkB,IACdrB,GAAgBH,EACnB,OAAOJ,EAAUoE,MAAMhE,EAAWE,GAAiBF,GAAYwB,GAAUtB,GACrE,KAAsB,GAAhBC,GAAqBX,EAAS,KACxC,OAAO4G,EAAgB5E,GAKzB,IAII+I,EAJA9E,EAAuG,MAA/FjE,GAAU,GAAMA,EAAS,EAAIzB,EAASyG,UAAUxG,GAAYwB,EAAS,EAAIjC,EAAIS,GAAY,IACjGwK,EAAQH,EAAS5E,GACjBgF,EAAgBzK,EAChB2C,EAAM3C,EAAWwB,EAAS,EAE1ByC,EAAI,EACR,GAAIuG,GAASA,EAAMtB,OAAS1H,EAAQ,CACnC,KAAOiJ,EAAgB9H,GAAK,CAE3B,GADA4H,EAAQxK,EAAS0G,UAAUgE,GACvBF,GAASC,EAAMvG,KAAM,CACxBwG,EAAgB,WAChB,KACA,CACDA,GAAiB,CACjB,CAED,IADA9H,GAAO,EACA8H,EAAgB9H,GAEtB,GADA4H,EAAQhL,EAAIkL,KACRF,GAASC,EAAMvG,KAAM,CACxBwG,EAAgB,WAChB,KACA,CAEF,GAAIA,IAAkB9H,EAErB,OADA3C,EAAWyK,EACJD,EAAMvE,OAEdtD,GAAO,EACP8H,EAAgBzK,CAChB,CAID,IAHAwK,EAAQ,GACRH,EAAS5E,GAAO+E,EAChBA,EAAMtB,MAAQ1H,EACPiJ,EAAgB9H,GACtB4H,EAAQxK,EAAS0G,UAAUgE,GAC3BD,EAAM/G,KAAK8G,GACXE,GAAiB,EAGlB,IADA9H,GAAO,EACA8H,EAAgB9H,GACtB4H,EAAQhL,EAAIkL,KACZD,EAAM/G,KAAK8G,GAGZ,IAAItE,EAASzE,EAAS,GAAK0E,EAAgB1E,GAAU2E,EAAe3E,GACpE,OACQgJ,EAAMvE,OADA,MAAVA,EACmBA,EACDG,EAAgB5E,EACvC,CAEA,SAAS8I,EAAaI,GAErB,GAAwB,iBAAbA,EAAuB,OAAOA,EACzC,GAAwB,iBAAbA,GAA6C,kBAAbA,GAA8C,iBAAbA,EAAuB,OAAOA,EAASzD,WACnH,GAAgB,MAAZyD,EAAkB,OAAOA,EAAW,GACxC,GAAIzK,EAAe0K,sBAAwB7E,MAAM8E,QAAQF,IAAaA,EAASG,OAAOC,OAAMC,GAAQ,CAAC,SAAU,SAAU,UAAW,UAAUC,gBAAgBD,KAC7J,OAAOL,EAASG,OAAO5D,WAExB,MAAM,IAAIjE,MAAM,4CAA4C0H,EAC7D,CAEA,MAAMlD,EAAmB,CAACnD,EAAID,KAC7B,IAAIL,EAAYY,IAAOb,IAAIwG,GAEvBW,EAAY5G,OACCnD,IAAbkD,IACHC,EAAKA,EAAK,MAAQD,GAAY,GAAKC,IAAQD,GAAY,GAAKC,EAC5DN,EAAUK,SAAWA,GAEtB,IAAI8G,EAAoBvL,EAAkB0E,GAS1C,OALI6G,IAAsBA,EAAkB/G,UAAYvD,MACtDjB,EAAkB4E,oBAAsB5E,EAAkB4E,kBAAoB,KAAKF,GAAM6G,GAE3FvL,EAAkB0E,GAAMN,EACxBA,EAAUY,KAAOY,EAAsBxB,EAAWkH,GAC3ClH,EAAUY,MAAM,EAExBvE,EAAkB,GAAK,OACvBA,EAAkB,GAAGsH,UAAW,EAEhCtH,EAAkB,IAAQ+K,IACzB,IAAIC,EAAcD,EAAKpI,WAAa,GAAM,EACtCsI,EAAOnE,OAAiB,IAAViE,EAAK,GAAYA,EAAK,GAAK,IAAQA,EAAK,IAC1D,IAAK,IAAIlH,EAAI,EAAGA,EAAImH,EAAYnH,IAC/BoH,IAASnE,OAAO,GAChBmE,GAAQnE,OAAOiE,EAAKlH,IAErB,GAAIkH,EAAKpI,aAAeqI,EAAY,CACnC,IAAIE,EAAO,IAAIzI,SAASsI,EAAK7I,OAAQ6I,EAAKrI,WAAYqI,EAAKpI,YACvDyB,EAAS,CAAC5B,EAAOD,KACpB,IAAInB,EAASmB,EAAMC,EACnB,GAAIpB,GAAU,GAAI,CACjB,IAAI+J,EAAMD,EAAKtE,aAAapE,GAC5B,IAAK,IAAIqB,EAAIrB,EAAQ,EAAGqB,EAAItB,EAAKsB,GAAK,EACrCsH,IAAQrE,OAAO,KACfqE,GAAOD,EAAKtE,aAAa/C,GAE1B,OAAOsH,CACP,CAED,IAAIC,EAAS5I,GAASpB,GAAU,GAAK,GACjCiK,EAAOjH,EAAO5B,EAAO4I,GACrBE,EAAQlH,EAAOgH,EAAQ7I,GAC3B,OAAQ8I,GAAQvE,OAAwB,GAAhBvE,EAAM6I,IAAgBE,GAE/CL,EAAQA,GAAQnE,OAAwC,GAAhCoE,EAAKvI,WAAaqI,IAAoB5G,EAAO4G,EAAYE,EAAKvI,WACtF,CACD,OAAOsI,GAGR,IAAIM,EAAS,CACZ3I,YAAO4I,oBAAW3G,sBAAY4G,8BAAgBC,wBAAaC,oBAAWC,kBAAUC,eAA0C,mBAAnBA,eAAgCA,eAAiB,MAEzJ7L,EAAkB,KAAQ,KACzB,IAAI+K,EAAOxG,IACX,IAAKgH,EAAOR,EAAK,IAAK,CACrB,IAAIzL,EAAQsD,MAAMmI,EAAK,GAAI,CAAEe,MAAOf,EAAK,KAEzC,OADAzL,EAAMgB,KAAOyK,EAAK,GACXzL,CACP,CACD,OAAOiM,EAAOR,EAAK,IAAIA,EAAK,GAAI,CAAEe,MAAOf,EAAK,IAAI,EAGnD/K,EAAkB,KAAS+K,IAE1B,IAAuC,IAAnClL,EAAekM,gBAA2B,MAAM,IAAInJ,MAAM,0CAC9D,IAAIqB,EAAKtE,EAAS0G,UAAUzG,EAAW,GAClCF,IACJA,EAAe,IAAI6F,KACpB,IACIyG,EADA/G,EAAQ9F,EAAIS,GAIfoM,EADG/G,GAAS,KAAQA,EAAQ,KAAiB,KAATA,GAA0B,KAATA,EAC5C,GACDA,GAAS,KAAQA,EAAQ,KAAiB,KAATA,GAA0B,KAATA,EACjD,IAAIM,KACJN,GAAS,KAAQA,GAAS,KAAQA,GAAS,KAAQA,GAAS,MAA+B,MAAtB9F,EAAIS,EAAW,GACpF,IAAIqM,IAEJ,CAAE,EAEZ,IAAIC,EAAW,CAAEF,UACjBtM,EAAa8F,IAAIvB,EAAIiI,GACrB,IAAIC,EAAmB5H,IACvB,IAAK2H,EAASE,KAEb,OAAOF,EAASF,OAASG,EAO1B,GAJC1K,OAAOC,OAAOsK,EAAQG,GAInBH,aAAkBzG,IACrB,IAAK,IAAKkE,EAAG4C,KAAMF,EAAiBG,UAAWN,EAAOxG,IAAIiE,EAAG4C,GAC9D,GAAIL,aAAkBC,IACrB,IAAK,IAAIpI,KAAK6B,MAAMrD,KAAK8J,GAAmBH,EAAOO,IAAI1I,GACxD,OAAOmI,GAGRhM,EAAkB,KAAS+K,IAE1B,IAAuC,IAAnClL,EAAekM,gBAA2B,MAAM,IAAInJ,MAAM,0CAC9D,IAAIqB,EAAKtE,EAAS0G,UAAUzG,EAAW,GACnCsM,EAAWxM,EAAa8M,IAAIvI,GAEhC,OADAiI,EAASE,MAAO,EACTF,EAASF,QAGjBhM,EAAkB,KAAQ,IAAM,IAAIiM,IAAI1H,KAEjC,MAAMkI,EAAc,CAAC,OAAO,QAAQ,eAAe,QAAQ,SAAS,QAAQ,SAAS,UAAU,UAAU,WAAW,aAAa/I,KAAIqG,GAAQA,EAAO,UAE3J,IAAI2C,EAA6B,iBAAfC,WAA0BA,WAAaC,OACzD5M,EAAkB,KAAS+K,IAC1B,IAAI8B,EAAW9B,EAAK,GAEhB7I,EAASI,WAAWN,UAAU4B,MAAM3B,KAAK8I,EAAM,GAAG7I,OAElD4K,EAAiBL,EAAYI,GACjC,IAAKC,EAAgB,CACpB,GAAiB,KAAbD,EAAiB,OAAO3K,EAC5B,GAAiB,KAAb2K,EAAiB,OAAO,IAAIpK,SAASP,GACzC,MAAM,IAAIU,MAAM,uCAAyCiK,EACzD,CACD,OAAO,IAAIH,EAAKI,GAAgB5K,EAAM,EAEvClC,EAAkB,KAAQ,KACzB,IAAI+K,EAAOxG,IACX,OAAO,IAAIwI,OAAOhC,EAAK,GAAIA,EAAK,GAAE,EAEnC,MAAMiC,EAAc,GAgCpB,SAASlL,EAAUmL,GAGlB,IAAIC,EAAc9N,EACd+N,EAAgBvN,EAEhBwN,EAAsBtN,EACtBuN,EAAoBtN,EACpBuN,EAAiB9N,EAEjB+N,EAAoB7N,EACpB8N,EAAsB/N,EAGtBgO,EAAW,IAAInL,WAAWnD,EAAIyE,MAAM,EAAGxE,IACvCsO,EAAkBnO,EAClBoO,EAA0BpO,EAAkBqE,MAAM,EAAGrE,EAAkB6B,QACvEwM,EAAa/N,EACbgO,EAAsBrN,EACtB2C,EAAQ8J,IAgBZ,OAfA7N,EAAS8N,EACTtN,EAAWuN,EAEXrN,EAAiBsN,EACjBrN,EAAesN,EACf7N,EAAY8N,EAEZ5N,EAAe6N,EACf9N,EAAiB+N,EACjBrO,EAAMsO,EACNjN,EAAiBqN,GACjBtO,EAAoBmO,GACFI,OAAO,EAAGvO,EAAkB6B,UAAWuM,GACzD9N,EAAiB+N,EACjBjO,EAAW,IAAI8C,SAAStD,EAAI+C,OAAQ/C,EAAIuD,WAAYvD,EAAIwD,YACjDQ,CACR,CACO,SAASpB,IACf5C,EAAM,KACNO,EAAe,KACfH,EAAoB,IACrB,CAxEAS,EAAkB,IAAS+K,IAC1B,IAAIgD,GAAYhD,EAAK,IAAM,KAAOA,EAAK,IAAM,KAAOA,EAAK,IAAM,GAAKA,EAAK,GACrEiD,EAAepO,EAQnB,OAPAA,GAAYmO,EAAWhD,EAAK3J,OAC5B3B,EAAiBuN,GACjBvN,EAAiB,CAACoK,IAAoBA,MACvB3D,UAAY,EAC3BzG,EAAewG,UAAY,EAC3BxG,EAAe+E,mBAAqB5E,EACpCA,EAAWoO,EACJzJ,GAAM,EAGdvE,EAAkB,KAAS+K,GAEP,GAAfA,EAAK3J,OACD,IAAI6M,KAA0E,KAA1D,SAAVlD,EAAK,IAAkBA,EAAK,IAAM,KAAOA,EAAK,IAAM,GAAKA,EAAK,KACxD,GAAfA,EAAK3J,OACN,IAAI6M,OACRlD,EAAK,IAAM,KAAOA,EAAK,IAAM,KAAOA,EAAK,IAAM,IAAMA,EAAK,IAAM,IAAM,IAC6B,KAAlF,YAAP,EAAVA,EAAK,IAAoC,SAAVA,EAAK,IAAkBA,EAAK,IAAM,KAAOA,EAAK,IAAM,GAAKA,EAAK,KACzE,IAAfA,EAAK3J,OACN,IAAI6M,OACRlD,EAAK,IAAM,KAAOA,EAAK,IAAM,KAAOA,EAAK,IAAM,GAAKA,EAAK,IAAM,IACoG,MAAxJ,IAAVA,EAAK,IAAc,gBAAkB,GAAe,cAAVA,EAAK,GAA+B,WAAVA,EAAK,GAA6B,SAAVA,EAAK,IAAkBA,EAAK,IAAM,KAAOA,EAAK,KAAO,GAAKA,EAAK,MAExJ,IAAIkD,KAAK,WAuDX,MAAMvH,EAAS,IAAIhB,MAAM,KAChC,IAAK,IAAI7B,EAAI,EAAGA,EAAI,IAAKA,IACxB6C,EAAO7C,KAAO,KAAOqK,KAAKC,MAAM,MAAY,OAAJtK,IAE5B,MAAAuK,EAAUzN,EACvB,IAAIyC,EAAiB,IAAIzC,EAAQ,CAAET,YAAY,IACnC,MAAC0B,GAASwB,EAAexB,OACxBkB,GAAiBM,EAAeN,eAChCsB,GAAShB,EAAexB,OACxByM,GAAkB,CAC9BC,MAAO,EACPC,OAAQ,EACRC,cAAe,EACfC,YAAa,GAEd,ICxrCIC,GAIAC,GAAYC,GDorCZC,GAAW,IAAIC,aAAa,GAC5BC,GAAU,IAAIzM,WAAWuM,GAAS3M,OAAQ,EAAG,GCxrCjD,IACCwM,GAAc,IAAIM,WACD,CAAhB,MAAO1P,GAAS,CAElB,MAAM2P,GAAkC,oBAAX7M,OACvB8M,GAAoBD,GACzB,SAAS7N,GAAU,OAAOgB,OAAO+M,gBAAgB/N,EAAO,EAAKkB,WACxD8M,GAAYH,GAAgB7M,OAASE,WACrC+M,GAAkBJ,GAAgB,WAAc,WACtD,IAAIjD,GAAQsD,GACRC,GAEAC,GADA5P,GAAW,EAEXH,GAAiB,KAErB,MACMgQ,GAAc,kBACPC,GAAgBC,OAAO,aAC7B,MAAMC,WAAcjP,EAC1BC,YAAYC,GAIX,IAAI2B,EACAqN,EACA5O,EACAvB,EANJoQ,MAAMjP,GACNc,KAAKoO,OAAS,EAMd,IAAIC,EAAaZ,GAAUpN,UAAUiO,UAAY,SAASpK,EAAQjG,GACjE,OAAOoM,GAAOiE,UAAUpK,EAAQjG,EAAUoM,GAAOrJ,WAAa/C,EACjE,KAAO8O,KAAeA,GAAYwB,aAC/B,SAASrK,EAAQjG,GAChB,OAAO8O,GAAYwB,WAAWrK,EAAQmG,GAAOzE,SAAS3H,IAAWuQ,OACrE,EAEMC,EAAQzO,KACPd,IACJA,EAAU,CAAE,GACb,IAAIwP,EAAexP,GAAWA,EAAQE,WAClCuP,EAAsBzP,EAAQI,YAAcJ,EAAQ0P,eACpDrP,EAAsBL,EAAQK,oBAGlC,GAF2B,MAAvBA,IACHA,EAAsBoP,EAAsB,GAAK,GAC9CpP,EAAsB,KACzB,MAAM,IAAI0B,MAAM,sCACb/B,EAAQkL,iBAAwCjL,MAArBD,EAAQ2P,YACtC7O,KAAK6O,WAAY,GAElB,IAAIC,EAAmB5P,EAAQ4P,iBACP,MAApBA,IACHA,EAAmBH,EAAsB,GAAK,IAC1C3O,KAAKV,YAAoC,GAAtBJ,EAAQX,aAC/ByB,KAAKV,WAAa,IAEnB,IAAIyP,EAAoBxP,EAAsB,IAAOuP,EAAmBvP,EAAsB,GAC1FyP,EAAgBzP,EAAsB,GACtC0P,EAAiB1P,EAAsBuP,EAAmB,GAC9D,GAAIG,EAAiB,KACpB,MAAM,IAAIhO,MAAM,wDAEjB,IAAIiO,EAAoB,GACpBC,EAAmB,EACnBC,EAAuC,EAE3CpP,KAAKqP,KAAOrP,KAAKsP,OAAS,SAAS9N,EAAO+N,GAwBzC,GAvBKlF,KACJA,GAAS,IAAIkD,GAAkB,MAC/BK,GAAavD,GAAOrM,WAAaqM,GAAOrM,SAAW,IAAI8C,SAASuJ,GAAO9J,OAAQ,EAAG,OAClFtC,GAAW,GAEZ4P,GAAUxD,GAAO5K,OAAS,GACtBoO,GAAU5P,GAAW,MAExBoM,GAAS,IAAIkD,GAAkBlD,GAAO5K,QACtCmO,GAAavD,GAAOrM,WAAaqM,GAAOrM,SAAW,IAAI8C,SAASuJ,GAAO9J,OAAQ,EAAG8J,GAAO5K,SACzFoO,GAAUxD,GAAO5K,OAAS,GAC1BxB,GAAW,GAEXA,GAAYA,GAAW,EAAK,WAC7B4C,EAAQ5C,GACJsR,EAAgBC,KAAqBvR,IAA6B,IAAhBsR,GACtDxR,EAAe0Q,EAAMrE,gBAAkB,IAAIxG,IAAQ,KAC/C6K,EAAMgB,eAAkC,iBAAVjO,GACjC1D,GAAiB,GACjBA,GAAeyD,KAAOxC,KAEtBjB,GAAiB,KAClBwB,EAAamP,EAAMnP,WACfA,EAAY,CACXA,EAAWK,gBACdL,EAAamP,EAAM9M,iBAAiB8M,EAAM/O,kBAC3C,IAAIF,EAAeF,EAAWE,cAAgB,EAC9C,GAAIA,EAAeD,EAElB,MAAM,IAAI0B,MAAM,qGAAuG3B,EAAWE,cAEnI,IAAKF,EAAWoQ,YAAa,CAE5BpQ,EAAWoQ,YAAc5P,OAAO6P,OAAO,MACvC,IAAK,IAAIzN,EAAI,EAAGA,EAAI1C,EAAc0C,IAAK,CACtC,IAAI0N,EAAOtQ,EAAW4C,GACtB,IAAK0N,EACJ,SACD,IAAIC,EAAgBC,EAAaxQ,EAAWoQ,YAC5C,IAAK,IAAI7H,EAAI,EAAG1F,EAAIyN,EAAKnQ,OAAQoI,EAAI1F,EAAG0F,IAAK,CAC5C,IAAInE,EAAMkM,EAAK/H,GACfgI,EAAiBC,EAAWpM,GACvBmM,IACJA,EAAiBC,EAAWpM,GAAO5D,OAAO6P,OAAO,OAElDG,EAAaD,CACb,CACDC,EAAW/B,IAAiB7L,EAAI,EAChC,CACDlC,KAAK+P,0BAA4BvQ,CACjC,CACIkP,IACJpP,EAAW0Q,OAASxQ,EAAe,GAEpC,CAGD,IAAIyQ,EAFA/B,IACHA,GAAkB,GAEnB,IACKO,EAAM9L,uBAAyBnB,GAASA,EAAMvC,aAAeuC,EAAMvC,cAAgBa,OACtFoQ,EAAY1O,GAEZ6N,EAAK7N,GACN,IAAI2O,EAAarS,GAGjB,GAFIA,IACHsS,GAAavP,EAAOwO,EAAM,GACvBtR,GAAgBA,EAAasS,YAAa,CAC7C,IAAIA,EAActS,EAAasS,YAAYC,MAAK,CAACjJ,EAAGC,IAAMD,EAAE+G,OAAS9G,EAAE8G,OAAS,GAAK,IACjFlM,EAAImO,EAAY5Q,OAChB8Q,GAAqB,EACzB,KAAOJ,GAAcjO,EAAI,GAAG,CAC3B,IAAIsO,EAAiBH,IAAcnO,GAAGkM,OAASvN,EAC3C2P,EAAkBL,EAAWM,gBAAkB5P,IAAiC,IAAvB0P,IAC5DA,EAAoB,GACjBC,EAAkBL,EAAWlS,SAAW4C,EACvC0P,GAAqB,IACxBA,GAAqB,IAElBA,GAAqB,IAExB3C,GAAW8C,UAAUP,EAAWlS,SAAW4C,EAC1C+M,GAAWlJ,UAAUyL,EAAWlS,SAAW4C,GAAS0P,GACrDA,GAAqB,GAEtBJ,EAAaA,EAAWQ,SACxBzO,IAED,CACGqO,GAAqB,GAAKJ,GAE7BvC,GAAW8C,UAAUP,EAAWlS,SAAW4C,EAC1C+M,GAAWlJ,UAAUyL,EAAWlS,SAAW4C,GAAS0P,GAEtDtS,IAAiC,EAArBoS,EAAY5Q,OACpBxB,GAAW4P,IACd+C,EAAS3S,IACVwQ,EAAML,OAASnQ,GACf,IAAI4S,EA04BT,SAAmBA,EAAYR,GAE9B,IAAIL,EACAc,EAAsC,EAArBT,EAAY5Q,OAC7BsR,EAAUF,EAAWpR,OAASqR,EAClC,KAAOd,EAASK,EAAYW,OAAO,CAClC,IAAI5C,EAAS4B,EAAO5B,OAChB9L,EAAK0N,EAAO1N,GAChBuO,EAAWI,WAAW7C,EAAS0C,EAAgB1C,EAAQ2C,GACvDD,GAAkB,EAClB,IAAI7S,EAAWmQ,EAAS0C,EACxBD,EAAW5S,KAAc,IACzB4S,EAAW5S,KAAc,IACzB4S,EAAW5S,KAAcqE,GAAM,GAC/BuO,EAAW5S,KAAeqE,GAAM,GAAM,IACtCuO,EAAW5S,KAAeqE,GAAM,EAAK,IACrCuO,EAAW5S,KAAmB,IAALqE,EACzByO,EAAU3C,CACV,CACD,OAAOyC,CACR,CA95BsBK,CAAU7G,GAAOzE,SAAS/E,EAAO5C,IAAWoS,GAE7D,OADAtS,EAAe,KACR8S,CACP,CAED,OADApC,EAAML,OAASnQ,GACXsR,EAAgB4B,IACnB9G,GAAOxJ,MAAQA,EACfwJ,GAAOzJ,IAAM3C,GACNoM,IAEDA,GAAOzE,SAAS/E,EAAO5C,GA4B9B,CA3BC,MAAMN,GAEP,MADAsS,EAAgBtS,EACVA,CACV,CAAa,QACT,GAAI2B,IACH8R,IACIlD,GAAmBO,EAAMG,gBAAgB,CAC5C,IAAIpP,EAAeF,EAAWE,cAAgB,EAE1C6R,EAAehH,GAAOzE,SAAS/E,EAAO5C,IACtCqT,EAi6BV,SAA2BhS,EAAYmP,GAOtC,OANAnP,EAAWiS,aAAgB1P,IAC1B,IAAI2P,GAAc3P,IAAwB4M,EAAMsB,2BAA6B,KAAOlO,EAAmBpC,OAGvG,OAFK+R,GACJ/C,EAAM9M,iBAAiBE,GACjB2P,CAAU,EAEXlS,CACR,CAz6B0BmS,CAAkBnS,EAAYmP,GAClD,IAAKwB,EACJ,OAAwE,IAApExB,EAAMG,eAAe0C,EAAeA,EAAcC,cAE9C9C,EAAMY,KAAK7N,EAAO+N,IAE1Bd,EAAMsB,0BAA4BvQ,EAE9B6K,GAAO5K,OAAS,aAAY4K,GAAS,MAClCgH,EAER,CAGEhH,GAAO5K,OAAS,aAAY4K,GAAS,MACrCkF,EAAgBmC,KACnBzT,GAAW4C,EACZ,CACD,EACD,MAAMuQ,EAAkB,KACnBhC,EAAuC,IAC1CA,IACD,IAAI5P,EAAeF,EAAWE,cAAgB,EAG9C,GAFIF,EAAWG,OAASD,IAAiBkP,IACxCpP,EAAWG,OAASD,GACjB2P,EAAmB,IAEtB7P,EAAWoQ,YAAc,KACzBN,EAAuC,EACvCD,EAAmB,EACfD,EAAkBzP,OAAS,IAC9ByP,EAAoB,SACf,GAAIA,EAAkBzP,OAAS,IAAMiP,EAAc,CACzD,IAAK,IAAIxM,EAAI,EAAGC,EAAI+M,EAAkBzP,OAAQyC,EAAIC,EAAGD,IACpDgN,EAAkBhN,GAAG6L,IAAiB,EAEvCmB,EAAoB,EACpB,GAEIyC,EAAanQ,IAClB,IAAI/B,EAAS+B,EAAM/B,OACfA,EAAS,GACZ4K,GAAOpM,MAAc,IAAOwB,EAClBA,EAAS,OACnB4K,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAcwB,GAAU,EAC/B4K,GAAOpM,MAAuB,IAATwB,IAErB4K,GAAOpM,MAAc,IACrB2P,GAAW8C,UAAUzS,GAAUwB,GAC/BxB,IAAY,GAEb,IAAK,IAAIiE,EAAI,EAAGA,EAAIzC,EAAQyC,IAC3BmN,EAAK7N,EAAMU,GACX,EAEImN,EAAQ7N,IACTvD,GAAW4P,KACdxD,GAASuG,EAAS3S,KAEnB,IACIwB,EADA2I,SAAc5G,EAElB,GAAa,WAAT4G,EAAmB,CACtB,IAkCIwJ,EAlCAC,EAAYrQ,EAAM/B,OACtB,GAAI3B,IAAkB+T,GAAa,GAAKA,EAAY,KAAQ,CAC3D,IAAK/T,GAAeyD,MAAQsQ,GAxOT,MAwOuC,CACzD,IAAIC,EAIA3B,EAHA4B,GAAYjU,GAAe,GAAgC,EAA3BA,GAAe,GAAG2B,OAAa3B,GAAe,GAAG2B,OAAS,GAAK,GAC/FxB,GAAW8T,EAAWlE,KACzBxD,GAASuG,EAAS3S,GAAW8T,IAE1BjU,GAAeG,UAClBkS,EAAarS,GACbuM,GAAOpM,IAAY,IACnBA,IAAY,EACZoM,GAAOpM,MAAc,GACrB6T,EAAW7T,GAAW4C,EACtB5C,IAAY,EACZmS,GAAavP,EAAOwO,EAAM,GAC1BzB,GAAWoE,UAAUF,EAAWjR,EAAQ,EAAG5C,GAAW4C,EAAQiR,KAE9DzH,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAc,GACrB6T,EAAW7T,GAAW4C,EACtB5C,IAAY,GAEbH,GAAiB,CAAC,GAAI,IACtBA,GAAe6S,SAAWR,EAC1BrS,GAAeyD,KAAO,EACtBzD,GAAeG,SAAW6T,CAC1B,CACD,IAAIG,EAAUnE,GAAYxH,KAAK9E,GAI/B,OAHA1D,GAAemU,EAAU,EAAI,IAAMzQ,EACnC6I,GAAOpM,MAAc,SACrBoR,EAAK4C,GAAWJ,EAAYA,EAE5B,CAIAD,EADGC,EAAY,GACF,EACHA,EAAY,IACT,EACHA,EAAY,MACT,EAEA,EAEd,IAAIE,EAAuB,EAAZF,EAIf,GAHI5T,GAAW8T,EAAWlE,KACzBxD,GAASuG,EAAS3S,GAAW8T,IAE1BF,EAAY,KAASxD,EAAY,CACpC,IAAInM,EAAGgQ,EAAIC,EAAIC,EAAcnU,GAAW2T,EACxC,IAAK1P,EAAI,EAAGA,EAAI2P,EAAW3P,IAC1BgQ,EAAK1Q,EAAM6Q,WAAWnQ,GAClBgQ,EAAK,IACR7H,GAAO+H,KAAiBF,EACdA,EAAK,MACf7H,GAAO+H,KAAiBF,GAAM,EAAI,IAClC7H,GAAO+H,KAAsB,GAALF,EAAY,KAElB,QAAZ,MAALA,IAC6C,QAAZ,OAAhCC,EAAK3Q,EAAM6Q,WAAWnQ,EAAI,MAE5BgQ,EAAK,QAAiB,KAALA,IAAgB,KAAY,KAALC,GACxCjQ,IACAmI,GAAO+H,KAAiBF,GAAM,GAAK,IACnC7H,GAAO+H,KAAiBF,GAAM,GAAK,GAAO,IAC1C7H,GAAO+H,KAAiBF,GAAM,EAAI,GAAO,IACzC7H,GAAO+H,KAAsB,GAALF,EAAY,MAEpC7H,GAAO+H,KAAiBF,GAAM,GAAK,IACnC7H,GAAO+H,KAAiBF,GAAM,EAAI,GAAO,IACzC7H,GAAO+H,KAAsB,GAALF,EAAY,KAGtCzS,EAAS2S,EAAcnU,GAAW2T,CACvC,MACKnS,EAAS4O,EAAW7M,EAAOvD,GAAW2T,GAGnCnS,EAAS,GACZ4K,GAAOpM,MAAc,IAAOwB,EAClBA,EAAS,KACfmS,EAAa,GAChBvH,GAAO4G,WAAWhT,GAAW,EAAGA,GAAW,EAAGA,GAAW,EAAIwB,GAE9D4K,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAcwB,GACXA,EAAS,OACfmS,EAAa,GAChBvH,GAAO4G,WAAWhT,GAAW,EAAGA,GAAW,EAAGA,GAAW,EAAIwB,GAE9D4K,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAcwB,GAAU,EAC/B4K,GAAOpM,MAAuB,IAATwB,IAEjBmS,EAAa,GAChBvH,GAAO4G,WAAWhT,GAAW,EAAGA,GAAW,EAAGA,GAAW,EAAIwB,GAE9D4K,GAAOpM,MAAc,IACrB2P,GAAW8C,UAAUzS,GAAUwB,GAC/BxB,IAAY,GAEbA,IAAYwB,CAChB,MAAU,GAAa,WAAT2I,EACV,GAAI5G,IAAU,IAAMA,EAEfA,EAAQ,IAASA,EAAQ,MAA4B,IAApBxB,KAAKzB,YAA0BiD,EAAQ,KAASxB,KAAK2C,sBACzF0H,GAAOpM,MAAcuD,EACXA,EAAQ,KAClB6I,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAcuD,GACXA,EAAQ,OAClB6I,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAcuD,GAAS,EAC9B6I,GAAOpM,MAAsB,IAARuD,IAErB6I,GAAOpM,MAAc,IACrB2P,GAAW8C,UAAUzS,GAAUuD,GAC/BvD,IAAY,QAEP,GAAIuD,GAAS,IAAMA,EACrBA,IAAU,GACb6I,GAAOpM,MAAc,IAAQuD,EACnBA,IAAU,KACpB6I,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAcuD,EAAQ,KACnBA,IAAU,OACpB6I,GAAOpM,MAAc,IACrB2P,GAAW0E,SAASrU,GAAUuD,GAC9BvD,IAAY,IAEZoM,GAAOpM,MAAc,IACrB2P,GAAW2E,SAAStU,GAAUuD,GAC9BvD,IAAY,OAEP,CACN,IAAI4G,EACJ,IAAKA,EAAa7E,KAAK6E,YAAc,GAAKrD,EAAQ,YAAeA,IAAU,WAAY,CAGtF,IAAIgR,EACJ,GAHAnI,GAAOpM,MAAc,IACrB2P,GAAW6E,WAAWxU,GAAUuD,GAE5BqD,EAAa,IAEb2N,EAAWhR,EAAQuD,GAA4B,IAAnBsF,GAAOpM,MAAqB,EAAMoM,GAAOpM,GAAW,IAAM,KAAQ,IAAOuU,EAExG,YADAvU,IAAY,GAGZA,IACD,CACDoM,GAAOpM,MAAc,IACrB2P,GAAW8E,WAAWzU,GAAUuD,GAChCvD,IAAY,CACZ,MACK,GAAa,WAATmK,GAA8B,aAATA,EAC/B,GAAK5G,EAEA,CACJ,GAAIzD,EAAc,CACjB,IAAI4U,EAAU5U,EAAa8M,IAAIrJ,GAC/B,GAAImR,EAAS,CACZ,IAAKA,EAAQrQ,GAAI,CAChB,IAAI+N,EAActS,EAAasS,cAAgBtS,EAAasS,YAAc,IAC1EsC,EAAQrQ,GAAK+N,EAAY3O,KAAKiR,EAC9B,CAKD,OAJAtI,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAc,IACrB2P,GAAW8C,UAAUzS,GAAU0U,EAAQrQ,SACvCrE,IAAY,EAEZ,CACAF,EAAa8F,IAAIrC,EAAO,CAAE4M,OAAQnQ,GAAW4C,GAC9C,CACD,IAAI5B,EAAcuC,EAAMvC,YACxB,GAAIA,IAAgBa,OACnB8S,EAAYpR,QACN,GAAIvC,IAAgB8E,MAC1B4N,EAAUnQ,QACJ,GAAIvC,IAAgB2E,IAC1B,GAAI5D,KAAK6S,iBAAkBxI,GAAOpM,MAAc,QAC3C,EACJwB,EAAS+B,EAAMD,MACF,GACZ8I,GAAOpM,MAAc,IAAOwB,EAClBA,EAAS,OACnB4K,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAcwB,GAAU,EAC/B4K,GAAOpM,MAAuB,IAATwB,IAErB4K,GAAOpM,MAAc,IACrB2P,GAAW8C,UAAUzS,GAAUwB,GAC/BxB,IAAY,GAEb,IAAK,IAAKyF,EAAKoP,KAAetR,EAC7B6N,EAAK3L,GACL2L,EAAKyD,EAEN,KACK,CACN,IAAK,IAAI5Q,EAAI,EAAGC,EAAI6K,GAAWvN,OAAQyC,EAAIC,EAAGD,IAAK,CAElD,GAAIV,aADiByL,GAAiB/K,GACD,CACpC,IAAIwD,EAAYsH,GAAW9K,GAC3B,GAAIwD,EAAUqN,MAAO,CAChBrN,EAAU0C,OACbiC,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAcyH,EAAU0C,KAC/BiC,GAAOpM,MAAc,GAEtB,IAAI+U,EAActN,EAAUqN,MAAMzS,KAAKN,KAAMwB,GAU7C,YATIwR,IAAgBxR,EACfuC,MAAM8E,QAAQrH,GACjBmQ,EAAUnQ,GAEVoR,EAAYpR,GAGb6N,EAAK2D,GAGN,CACD,IAIItQ,EAJAuQ,EAAgB5I,GAChB6I,EAAoBtF,GACpBuF,EAAkBlV,GACtBoM,GAAS,KAET,IACC3H,EAASgD,EAAU2J,KAAK/O,KAAKN,KAAMwB,GAAQD,IAE1C8I,GAAS4I,EACTA,EAAgB,KAChBhV,IAAYsD,EACRtD,GAAW4P,IACd+C,EAAS3S,IACH,CACNoM,UAAQuD,cAAY3P,SAAUA,GAAWsD,KAExC8N,EASH,CARS,QAEL4D,IACH5I,GAAS4I,EACTrF,GAAasF,EACbjV,GAAWkV,EACXtF,GAAUxD,GAAO5K,OAAS,GAE3B,CAMD,YALIiD,IACCA,EAAOjD,OAASxB,GAAW4P,IAC9B+C,EAASlO,EAAOjD,OAASxB,IAC1BA,GAAWmV,GAAmB1Q,EAAQ2H,GAAQpM,GAAUyH,EAAU0C,OAGnE,CACD,CAED,GAAIrE,MAAM8E,QAAQrH,GACjBmQ,EAAUnQ,OACJ,CAEN,GAAIA,EAAM6R,OAAQ,CACjB,MAAMC,EAAO9R,EAAM6R,SAEnB,GAAIC,IAAS9R,EACZ,OAAO6N,EAAKiE,EACb,CAGD,GAAa,aAATlL,EACH,OAAOiH,EAAKrP,KAAKuT,eAAiBvT,KAAKuT,cAAc/R,IAGtDoR,EAAYpR,EACZ,CACD,CACD,MAvHA6I,GAAOpM,MAAc,SAwHhB,GAAa,YAATmK,EACViC,GAAOpM,MAAcuD,EAAQ,IAAO,SAC9B,GAAa,WAAT4G,EAAmB,CAC7B,GAAI5G,EAAQ,oBAAsBA,IAAU,mBAE3C6I,GAAOpM,MAAc,IACrB2P,GAAW4F,YAAYvV,GAAUuD,QAC3B,GAAIA,EAAQ,qBAAuBA,EAAQ,EAEjD6I,GAAOpM,MAAc,IACrB2P,GAAW6F,aAAaxV,GAAUuD,OAC5B,CAEN,IAAIxB,KAAK0T,mBAGF,IAAI1T,KAAK2T,oBACf,OAAOtE,EAAK7N,EAAM0D,YACZ,GAAIlF,KAAK4T,oBAAsB5T,KAAK6O,UAAW,CACrD,IAEI/K,EAFA+P,EAAQrS,EAAQ,EAAI2D,QAAQ,GAAKA,OAAO,GAG5C,GAAI3D,GAAS2D,OAAO,SAAa0O,EAAO,CACvC,IAAIC,EAAO3O,OAAO,qBAAuBA,OAAO,GAC5C4O,EAAS,GACb,KACCA,EAAOrS,KAAKF,EAAQsS,GACftS,GAAS2D,OAAO,MAAS0O,GAC9BrS,IAAU2D,OAAO,IAGlBrB,EAAQ,IAAInD,WAAW,IAAIqT,eAAeD,GAAQxT,QAClDuD,EAAMmQ,SACb,KAAa,CACN,IAAIC,EAAS1S,EAAQ,EACjB0C,GAAUgQ,GAAU1S,EAAQA,GAAO0D,SAAS,IAOhD,GANIhB,EAAOzE,OAAS,EACnByE,EAAS,IAAMA,EACLiQ,SAASjQ,EAAOkQ,OAAO,GAAI,KAAO,IAC5ClQ,EAAS,KAAOA,GAGboJ,GACHxJ,EAAQrD,OAAOC,KAAKwD,EAAQ,WACtB,CACNJ,EAAQ,IAAInD,WAAWuD,EAAOzE,OAAS,GACvC,IAAK,IAAIyC,EAAI,EAAGA,EAAI4B,EAAMrE,OAAQyC,IACjC4B,EAAM5B,GAAKiS,SAASjQ,EAAOjC,MAAU,EAAJC,EAAW,EAAJA,EAAQ,GAAI,GAErD,CAED,GAAIgS,EACH,IAAK,IAAIhS,EAAI,EAAGA,EAAI4B,EAAMrE,OAAQyC,IAAK4B,EAAM5B,IAAM4B,EAAM5B,EAE1D,CAKD,OAHI4B,EAAMrE,OAASxB,GAAW4P,IAC7B+C,EAAS9M,EAAMrE,OAASxB,SACzBA,GAAWmV,GAAmBtP,EAAOuG,GAAQpM,GAAU,IAE7D,CACM,MAAM,IAAIiF,WAAW1B,0LAGrB,CAlDA6I,GAAOpM,MAAc,IACrB2P,GAAW8E,WAAWzU,GAAUmH,OAAO5D,GAkDxC,CACDvD,IAAY,CAChB,KAAU,IAAa,cAATmK,EASV,MAAM,IAAInH,MAAM,iBAAmBmH,GAR/BpI,KAAKqU,qBACRhK,GAAOpM,MAAc,KAErBoM,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAc,EACrBoM,GAAOpM,MAAc,EAItB,GAGIqW,EAAoBtU,KAAKuU,iBAAmBvU,KAAKwU,sBAAwBxU,KAAKyU,WAAehR,IAElG,IAAImM,EACJ,GAAI5P,KAAKyU,WAAY,CACpB7E,EAAO,GACP,IAAK,IAAIlM,KAAOD,EACuB,mBAA1BA,EAAOiR,iBAAiCjR,EAAOiR,eAAehR,IACxE1D,KAAKyU,WAAWxL,SAASxF,EAAOC,KACjCkM,EAAKlO,KAAKgC,EAEhB,MACIkM,EAAO9P,OAAO8P,KAAKnM,GAEpB,IAYIC,EAZAjE,EAASmQ,EAAKnQ,OAalB,GAZIA,EAAS,GACZ4K,GAAOpM,MAAc,IAAOwB,EAClBA,EAAS,OACnB4K,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAcwB,GAAU,EAC/B4K,GAAOpM,MAAuB,IAATwB,IAErB4K,GAAOpM,MAAc,IACrB2P,GAAW8C,UAAUzS,GAAUwB,GAC/BxB,IAAY,GAGT+B,KAAKwU,qBACR,IAAK,IAAItS,EAAI,EAAGA,EAAIzC,EAAQyC,IAAK,CAChCwB,EAAMkM,EAAK1N,GACX,IAAIyS,EAAMvP,OAAO1B,GACjB2L,EAAKuF,MAAMD,GAAOjR,EAAMiR,GACxBtF,EAAK5L,EAAOC,GACZ,MAGD,IAAK,IAAIxB,EAAI,EAAGA,EAAIzC,EAAQyC,IAC3BmN,EAAK3L,EAAMkM,EAAK1N,IAChBmN,EAAK5L,EAAOC,GAEb,EAEDD,IACA4G,GAAOpM,MAAc,IACrB,IAAI4W,EAAe5W,GAAW4C,EAC9B5C,IAAY,EACZ,IAAIsD,EAAO,EACX,IAAK,IAAImC,KAAOD,GACsB,mBAA1BA,EAAOiR,gBAAiCjR,EAAOiR,eAAehR,MACxE2L,EAAK3L,GACL2L,EAAK5L,EAAOC,IACZnC,KAGF,GAAIA,EAAO,MACV,MAAM,IAAIN,MAAM,yHAGjBoJ,GAAOwK,IAAiBhU,GAASU,GAAQ,EACzC8I,GAAOwK,EAAehU,GAAgB,IAAPU,CAAW,EAGrCuT,GAAkC,IAApB9U,KAAKzB,WAAuB+V,EAC/CpV,EAAQ6V,qBAAuBhG,EAC/BtL,IACA,IAAIoM,EAEAmF,EAFgBlF,EAAaxQ,EAAWoQ,cAAgBpQ,EAAWoQ,YAAc5P,OAAO6P,OAAO,OAC/FkF,EAAe5W,KAAa4C,EAEhC,IAAK,IAAI6C,KAAOD,EACf,GAAqC,mBAA1BA,EAAOiR,gBAAiCjR,EAAOiR,eAAehR,GAAM,CAE9E,GADAmM,EAAiBC,EAAWpM,GACxBmM,EACHC,EAAaD,MACT,CAEJ,IAAID,EAAO9P,OAAO8P,KAAKnM,GACnBwR,EAAiBnF,EACrBA,EAAaxQ,EAAWoQ,YACxB,IAAIwF,EAAiB,EACrB,IAAK,IAAIhT,EAAI,EAAGC,EAAIyN,EAAKnQ,OAAQyC,EAAIC,EAAGD,IAAK,CAC5C,IAAIwB,EAAMkM,EAAK1N,GACf2N,EAAiBC,EAAWpM,GACvBmM,IACJA,EAAiBC,EAAWpM,GAAO5D,OAAO6P,OAAO,MACjDuF,KAEDpF,EAAaD,CACb,CACGgF,EAAehU,EAAQ,GAAK5C,IAE/BA,KACAkX,EAAUrF,EAAYF,EAAMsF,IAE5BE,EAAgBtF,EAAYF,EAAMiF,EAAcK,GACjDF,GAAY,EACZlF,EAAamF,EAAevR,EAC5B,CACD2L,EAAK5L,EAAOC,GACZ,CAEF,IAAKsR,EAAW,CACf,IAAIK,EAAWvF,EAAW/B,IACtBsH,EACHhL,GAAOwK,EAAehU,GAASwU,EAE/BD,EAAgBtF,EAAYhQ,OAAO8P,KAAKnM,GAASoR,EAAc,EAChE,GAEDpR,IACA,IAAIoM,EAAgBC,EAAaxQ,EAAWoQ,cAAgBpQ,EAAWoQ,YAAc5P,OAAO6P,OAAO,OAC/FuF,EAAiB,EACrB,IAAK,IAAIxR,KAAOD,GAA6C,mBAA1BA,EAAOiR,gBAAiCjR,EAAOiR,eAAehR,MAChGmM,EAAiBC,EAAWpM,GACvBmM,IACJA,EAAiBC,EAAWpM,GAAO5D,OAAO6P,OAAO,MACjDuF,KAEDpF,EAAaD,GAEd,IAAIwF,EAAWvF,EAAW/B,IACtBsH,EACCA,GAAY,IAAQtG,GACvB1E,GAAOpM,MAA4C,IAAR,IAApBoX,GAAY,KACnChL,GAAOpM,MAAcoX,GAAY,GAEjChL,GAAOpM,MAAcoX,EAEtBF,EAAUrF,EAAYA,EAAWwF,UAAYxV,OAAO8P,KAAKnM,GAASyR,GAGnE,IAAK,IAAIxR,KAAOD,GACsB,mBAA1BA,EAAOiR,gBAAiCjR,EAAOiR,eAAehR,KACxE2L,EAAK5L,EAAOC,GACZ,EAIG6R,EAA4C,mBAAnBvV,KAAKzB,YAA4ByB,KAAKzB,WAE/DqU,EAAc2C,EAAmB9R,IACtC8R,EAAgB9R,GAAUqR,EAAYrR,GAAU6Q,EAAiB7Q,EAAO,EACrEqR,EAEElE,EAAYhQ,IACjB,IAAI4U,EACJ,GAAI5U,EAAM,SAAW,CAEpB,GAAKA,EAAMC,EAAS6M,GACnB,MAAM,IAAIzM,MAAM,0DACjBuU,EAAUjJ,KAAKkJ,IAAI/H,GACsE,KAAxFnB,KAAKmJ,MAAMnJ,KAAKoJ,KAAK/U,EAAMC,IAAUD,EAAM,SAAY,KAAO,GAAI,SAAY,MAC/E,MACA4U,EAAqE,GAAzDjJ,KAAKoJ,IAAK/U,EAAMC,GAAU,EAAGwJ,GAAO5K,OAAS,IAAM,KAAY,GAC5E,IAAImW,EAAY,IAAIrI,GAAkBiI,GAUtC,OATA5H,GAAagI,EAAU5X,WAAa4X,EAAU5X,SAAW,IAAI8C,SAAS8U,EAAUrV,OAAQ,EAAGiV,IAC3F5U,EAAM2L,KAAKkJ,IAAI7U,EAAKyJ,GAAO5K,QACvB4K,GAAOwL,KACVxL,GAAOwL,KAAKD,EAAW,EAAG/U,EAAOD,GAEjCgV,EAAU/R,IAAIwG,GAAOpI,MAAMpB,EAAOD,IACnC3C,IAAY4C,EACZA,EAAQ,EACRgN,GAAU+H,EAAUnW,OAAS,GACtB4K,GAASuL,GAEXT,EAAY,CAACrF,EAAYF,EAAMsF,KACpC,IAAIG,EAAW/V,EAAW0Q,OACrBqF,IACJA,EAAW,IACRA,EAAWrG,GAAiBhP,KAAK8V,uBAAyB9V,KAAK8V,qBAAqBlG,IACvFyF,EAAW/V,EAAWyW,UAChBV,EAAWpG,IAChBoG,EAAWrG,GACZ1P,EAAWyW,UAAYV,EAAW,IAE9BA,GAAYpG,IACfoG,EAAWrG,GACZ1P,EAAW0Q,OAASqF,EAAW,GAEhC,IAAIhT,EAAWuN,EAAKvN,SAAWgT,GAAY,IAAQtG,EAAqBsG,EAAW,IAAS,GAAK,EACjGvF,EAAW/B,IAAiBsH,EAC5BvF,EAAWwF,SAAW1F,EACtBtQ,EAAW+V,EAAW,IAAQzF,EAE1ByF,EAAWrG,GACdY,EAAKxN,UAAW,EAChB9C,EAAWE,aAAe6V,EAAW,GACrCnH,GAAkB,EACd7L,GAAY,GACfgI,GAAOpM,MAAkC,IAAR,GAAXoX,GACtBhL,GAAOpM,MAAcoE,GAErBgI,GAAOpM,MAAcoX,IAGlBhT,GAAY,GACfgI,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAkC,IAAR,GAAXoX,GACtBhL,GAAOpM,MAAcoE,IAErBgI,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAc,IACrBoM,GAAOpM,MAAcoX,GAGlBH,IACH/F,GAAoBC,EAAuC8F,GAExDhG,EAAkBzP,QAAUqP,IAC/BI,EAAkB8G,QAAQjI,IAAiB,GAC5CmB,EAAkBxN,KAAKoO,GACvBT,EAAKO,GACL,EAEIwF,EAAkB,CAACtF,EAAYF,EAAMqG,EAAiBf,KAC3D,IAAIgB,EAAa7L,GACb8L,EAAelY,GACfmY,EAAcvI,GACdwI,EAAYxV,EAChBwJ,GAASsD,GACT1P,GAAW,EACX4C,EAAQ,EACHwJ,KACJsD,GAAatD,GAAS,IAAIkD,GAAkB,OAC7CM,GAAUxD,GAAO5K,OAAS,GAC1B0V,EAAUrF,EAAYF,EAAMsF,GAC5BvH,GAAatD,GACb,IAAIiM,EAAerY,GAKnB,GAJAoM,GAAS6L,EACTjY,GAAWkY,EACXtI,GAAUuI,EACVvV,EAAQwV,EACJC,EAAe,EAAG,CACrB,IAAIC,EAAStY,GAAWqY,EAAe,EACnCC,EAAS1I,IACZ+C,EAAS2F,GACV,IAAIC,EAAoBP,EAAkBpV,EAC1CwJ,GAAO4G,WAAWuF,EAAoBF,EAAcE,EAAoB,EAAGvY,IAC3EoM,GAAOxG,IAAI8J,GAAW1L,MAAM,EAAGqU,GAAeE,GAC9CvY,GAAWsY,CACf,MACIlM,GAAO4L,EAAkBpV,GAAS8M,GAAW,EAC7C,EAEIuC,EAAezM,IACpB,IAAIgT,EA/zBHC,UA+zBkCjT,EAAQ4G,GAAQxJ,EAAO5C,GAAUqB,EAAYsR,GAAU,CAACpP,EAAOiV,EAAaE,KAC9G,GAAIA,EACH,OAAOzI,GAAkB,EAC1BjQ,GAAWwY,EACX,IAAIG,EAAcvM,GAGlB,OAFAgF,EAAK7N,GACL4P,IACIwF,IAAgBvM,GACZ,CAAEpM,YAAU2P,cAAYvD,WAEzBpM,EAAQ,GACb+B,MACH,GAAoB,IAAhByW,EACH,OAAO7D,EAAYnP,GACpBxF,GAAWwY,CAAW,CAEvB,CACDI,UAAUtW,GAET8J,GAAS9J,EACT8J,GAAOrM,WAAaqM,GAAOrM,SAAW,IAAI8C,SAASuJ,GAAO9J,OAAQ8J,GAAOtJ,WAAYsJ,GAAOrJ,aAC5F4M,GAAavD,GAAOrM,SACpBC,GAAW,CACX,CACGA,aAAUuD,GACbvD,GAAWuD,CACX,CACGvD,eACH,OAAOA,EACP,CACD6Y,kBACK9W,KAAKV,aACRU,KAAKV,WAAa,IACfU,KAAK+W,eACR/W,KAAK+W,aAAe,GACrB,EAwGF,SAASC,GAAeC,EAAY7O,EAAM8O,EAAkB5H,GAC3D,IAAI7P,EAASwX,EAAWjW,WACxB,GAAIvB,EAAS,EAAI,IAAO,CACvB,IAAI4K,OAAEA,EAAMpM,SAAEA,GAAaiZ,EAAiB,EAAIzX,GAChD4K,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAcwB,EAAS,CAChC,MAAQ,GAAIA,EAAS,EAAI,MAAS,CAChC,IAAI4K,OAAEA,EAAMpM,SAAEA,GAAaiZ,EAAiB,EAAIzX,GAChD4K,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAewB,EAAS,GAAM,EACrC4K,EAAOpM,KAAewB,EAAS,EAAK,GACtC,KAAQ,CACN,IAAI4K,OAAEA,EAAMpM,SAAEA,EAAQ2P,WAAEA,GAAesJ,EAAiB,EAAIzX,GAC5D4K,EAAOpM,KAAc,IACrB2P,EAAW8C,UAAUzS,EAAUwB,EAAS,GACxCxB,GAAY,CACZ,CACDoM,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAcmK,EAChB6O,EAAW1W,SAAQ0W,EAAa,IAAItW,WAAWsW,IACpD5M,EAAOxG,IAAI,IAAIlD,WAAWsW,EAAW1W,OAAQ0W,EAAWlW,WAAYkW,EAAWjW,YAAa/C,EAC7F,CACA,SAASkZ,GAAY5W,EAAQ2W,GAC5B,IAAIzX,EAASc,EAAOS,WACpB,IAAIqJ,EAAQpM,EACZ,GAAIwB,EAAS,IAAO,CACnB,IAAI4K,OAAEA,EAAMpM,SAAEA,GAAaiZ,EAAiBzX,EAAS,GACrD4K,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAcwB,CACvB,MAAQ,GAAIA,EAAS,MAAS,CAC5B,IAAI4K,OAAEA,EAAMpM,SAAEA,GAAaiZ,EAAiBzX,EAAS,GACrD4K,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAcwB,GAAU,EAC/B4K,EAAOpM,KAAuB,IAATwB,CACvB,KAAQ,CACN,IAAI4K,OAAEA,EAAMpM,SAAEA,EAAQ2P,WAAEA,GAAesJ,EAAiBzX,EAAS,GACjE4K,EAAOpM,KAAc,IACrB2P,EAAW8C,UAAUzS,EAAUwB,GAC/BxB,GAAY,CACZ,CACDoM,EAAOxG,IAAItD,EAAQtC,EACpB,CAEA,SAASmV,GAAmB1Q,EAAQ2H,EAAQpM,EAAUmK,GACrD,IAAI3I,EAASiD,EAAOjD,OACpB,OAAQA,GACP,KAAK,EACJ4K,EAAOpM,KAAc,IACrB,MACD,KAAK,EACJoM,EAAOpM,KAAc,IACrB,MACD,KAAK,EACJoM,EAAOpM,KAAc,IACrB,MACD,KAAK,EACJoM,EAAOpM,KAAc,IACrB,MACD,KAAK,GACJoM,EAAOpM,KAAc,IACrB,MACD,QACKwB,EAAS,KACZ4K,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAcwB,GACXA,EAAS,OACnB4K,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAcwB,GAAU,EAC/B4K,EAAOpM,KAAuB,IAATwB,IAErB4K,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAcwB,GAAU,GAC/B4K,EAAOpM,KAAewB,GAAU,GAAM,IACtC4K,EAAOpM,KAAewB,GAAU,EAAK,IACrC4K,EAAOpM,KAAuB,IAATwB,GAMxB,OAHA4K,EAAOpM,KAAcmK,EACrBiC,EAAOxG,IAAInB,EAAQzE,GACnBA,GAAYwB,CAEb,CAwBA,SAAS2Q,GAAavP,EAAOwO,EAAMkB,GAClC,GAAIzS,GAAe2B,OAAS,EAAG,CAC9BmO,GAAW8C,UAAU5S,GAAeG,SAAW4C,EAAO5C,GAAWsS,EAAoBzS,GAAeG,SAAW4C,GAC/G/C,GAAe2S,gBAAkBxS,GAAW4C,EAC5C,IAAIuW,EAAetZ,GACnBA,GAAiB,KACjBuR,EAAK+H,EAAa,IAClB/H,EAAK+H,EAAa,GAClB,CACF,CAvNAnK,GAAmB,CAAEX,KAAMhC,IAAKrJ,MAAOmK,OAAQ5K,YAAaV,OAAOuX,eAAe1W,WAAWN,WAAWpB,YAA4B6B,SAAUrC,GAC9IuO,GAAa,CAAC,CACbqC,KAAKiI,EAAMJ,EAAkB7H,GAC5B,IAAIkI,EAAUD,EAAKE,UAAY,IAC/B,IAAKxX,KAAKyX,gBAA6C,IAA3BH,EAAKI,oBAA4BH,GAAW,GAAKA,EAAU,WAAa,CAEnG,IAAIlN,OAAEA,EAAMuD,WAAEA,EAAU3P,SAAEA,GAAYiZ,EAAiB,GACvD7M,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,IACrB2P,EAAW8C,UAAUzS,EAAUsZ,EAC/B,MAAM,GAAIA,EAAU,GAAKA,EAAU,WAAa,CAEhD,IAAIlN,OAAEA,EAAMuD,WAAEA,EAAU3P,SAAEA,GAAYiZ,EAAiB,IACvD7M,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,IACrB2P,EAAW8C,UAAUzS,EAAmC,IAAzBqZ,EAAKI,mBAAgCH,EAAU,IAAO,YAAgB,IACrG3J,EAAW8C,UAAUzS,EAAW,EAAGsZ,EACtC,MAAS,GAAI3C,MAAM2C,GAAU,CAC1B,GAAIvX,KAAK2X,cAER,OADAT,EAAiB,GACV7H,EAAKrP,KAAK2X,iBAGlB,IAAItN,OAAEA,EAAMuD,WAAEA,EAAU3P,SAAEA,GAAYiZ,EAAiB,GACvD7M,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,GACxB,KAAS,CAEN,IAAIoM,OAAEA,EAAMuD,WAAEA,EAAU3P,SAAEA,GAAYiZ,EAAiB,IACvD7M,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,GACrBoM,EAAOpM,KAAc,IACrB2P,EAAW8C,UAAUzS,EAAmC,IAAzBqZ,EAAKI,mBACpC9J,EAAW4F,YAAYvV,EAAW,EAAGkH,OAAOoH,KAAKC,MAAM+K,IACvD,CACD,GACC,CACFlI,KAAKxL,EAAKqT,EAAkB7H,GAC3B,GAAIrP,KAAK4X,iBAER,OADAV,EAAiB,GACV7H,EAAK,CAAA,GAEb,IAAIvL,EAAQC,MAAMrD,KAAKmD,IACnBwG,OAAEA,EAAMpM,SAAEA,GAAYiZ,EAAiBlX,KAAK6O,UAAY,EAAI,GAC5D7O,KAAK6O,YACRxE,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,GAEtBoR,EAAKvL,EACL,GACC,CACFuL,KAAK1R,EAAOuZ,EAAkB7H,GAC7B,IAAIhF,OAAEA,EAAMpM,SAAEA,GAAYiZ,EAAiBlX,KAAK6O,UAAY,EAAI,GAC5D7O,KAAK6O,YACRxE,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,GAEtBoR,EAAK,CAAE1R,EAAMgB,KAAMhB,EAAMwF,QAASxF,EAAMwM,OACxC,GACC,CACFkF,KAAKwI,EAAOX,EAAkB7H,GAC7B,IAAIhF,OAAEA,EAAMpM,SAAEA,GAAYiZ,EAAiBlX,KAAK6O,UAAY,EAAI,GAC5D7O,KAAK6O,YACRxE,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,IACrBoM,EAAOpM,KAAc,GAEtBoR,EAAK,CAAEwI,EAAM3X,OAAQ2X,EAAMC,OAC3B,GACC,CACFzI,KAAK0I,EAAab,GACblX,KAAK6O,UACRmI,GAAee,EAAa,GAAMb,GAElCC,GAAY7J,GAAgB7M,OAAOC,KAAKqX,GAAe,IAAIpX,WAAWoX,GAAcb,EACrF,GACC,CACF7H,KAAK4H,EAAYC,GAChB,IAAIjY,EAAcgY,EAAWhY,YACzBA,IAAgBwO,IAAazN,KAAK6O,UACrCmI,GAAeC,EAAYnM,EAAYkN,QAAQ/Y,EAAYN,MAAOuY,GAElEC,GAAYF,EAAYC,EACzB,GACC,CACF7H,KAAK0I,EAAab,GACblX,KAAK6O,UACRmI,GAAee,EAAa,GAAMb,GAElCC,GAAY7J,GAAgB7M,OAAOC,KAAKqX,GAAe,IAAIpX,WAAWoX,GAAcb,EACrF,GACC,CACF7H,KAAK6C,EAAIgF,GACR,IAAI7M,OAAEA,EAAMpM,SAAEA,GAAYiZ,EAAiB,GAC3C7M,EAAOpM,GAAY,GACnB,IAgJF,IAAIga,GAAe,IAAIhK,GAAM,CAAE1P,YAAY,IAC/B,MAAC8Q,GAAO4I,GAAa5I,KACpBC,GAAS2I,GAAa5I,KACtB6I,GAAUjK,IAGVtB,MAAEA,GAAKC,OAAEA,GAAMC,cAAEA,GAAaC,YAAEA,IAAgBJ,GAChDyE,GAAoB,IACpBO,GAAoB,KACpBlC,GAAsB,KC3hCtB,MAAA2I,GA1CN,SAAqBC,EAAgBlZ,EAAU,IACpD,IAAKkZ,GAA4C,iBAAnBA,EAC5B,MAAM,IAAInX,MAAM,8FAGlB,MAAMoX,EAAU,IAAIrZ,EAAQE,GAC5B,IAAImE,EACJ,MAAMiV,EAAU9P,IACd,IAAI+P,EAEAlV,IACFmF,EAAQ/H,OAAO+X,OAAO,CAACnV,EAAYmF,IACnCnF,OAAalE,GAGf,IACEoZ,EAASF,EAAQlX,eAAeqH,EAQjC,CAPC,MAAOiQ,GACP,IAAIA,EAAIpV,WAIN,MAAMoV,EAHNpV,EAAamF,EAAMvG,MAAMwW,EAAInX,cAC7BiX,EAASE,EAAIpX,MAIhB,CACD,OAAOkX,GAGT,MAA+C,mBAApCH,EAAepK,OAAO0K,UACxB,YACL,IAAK,MAAMlX,KAAS4W,QACVE,EAAO9W,EAElB,CAJM,GAKkD,mBAAzC4W,EAAepK,OAAO2K,eAC/B,kBACL,UAAW,MAAMnX,KAAS4W,QAChBE,EAAO9W,EAElB,CAJM,QADF,CAOT,EAEaoX,GA5EN,SAAmBC,EAAgB3Z,EAAU,IAClD,GAAK2Z,GAA4C,iBAAnBA,EAEvB,IAA+C,mBAApCA,EAAe7K,OAAO0K,UACtC,OAQJ,UAAyBG,EAAgB3Z,GACvC,MAAMuP,EAAQ,IAAIR,GAAM/O,GACxB,IAAK,MAAMsC,KAASqX,QACZpK,EAAMY,KAAK7N,EAErB,CAbWsX,CAAaD,EAAgB3Z,GAC/B,GAAmC,mBAAxB2Z,EAAeE,MAAuE,mBAAzCF,EAAe7K,OAAO2K,eACnF,OAaJK,gBAAgCH,EAAgB3Z,GAC9C,MAAMuP,EAAQ,IAAIR,GAAM/O,GACxB,UAAW,MAAMsC,KAASqX,QAClBpK,EAAMY,KAAK7N,EAErB,CAlBWyX,CAAcJ,EAAgB3Z,GAErC,MAAM,IAAI+B,MAAM,6FACjB,CAPC,MAAM,IAAIA,MAAM,yFAQpB,+ND0jCO,SAAsByE,GAC5B,GAAIA,EAAUwT,MAAO,CACpB,IAAKxT,EAAU2J,OAAS3J,EAAUqN,MACjC,MAAM,IAAI9R,MAAM,2CACjB,GAAIyE,EAAU2J,OAAS3J,EAAU0C,KAChC,MAAM,IAAInH,MAAM,kEACjBgM,GAAiBkM,QAAQzT,EAAUwT,OACnClM,GAAWmM,QAAQzT,EACnB,ED6EK,SAAsBA,GACxBA,EAAUzF,OACb5B,EAAkBqH,EAAU0C,MAAQ1C,EAAUzF,OAE9C5B,EAAkBqH,EAAU0C,MAAQ1C,CACtC,CCjFC0T,CAAmB1T,EACpB,yGDriByC,mBG/iBZ,2BHurCtB,SAAsB2T,GAC5BnM,GAAS,GAAKmM,EACd,IAAIvU,EAAaC,GAAsB,IAAbqI,GAAQ,KAAc,EAAMA,GAAQ,IAAM,GACpE,OAAStI,EAAauU,GAAiBA,EAAgB,EAAI,IAAO,KAAS,GAAKvU,CACjF,gDG5rC0B"}