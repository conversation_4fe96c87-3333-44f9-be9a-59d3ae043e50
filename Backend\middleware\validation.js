const { body, param, query, validationResult } = require('express-validator');
const ErrorResponse = require('../utils/errorResponse');

/**
 * Input validation and sanitization middleware
 */

/**
 * Handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value
    }));
    
    return next(new ErrorResponse('Validation failed', 400, { errors: errorMessages }));
  }
  
  next();
};

/**
 * Sanitize all string inputs
 */
const sanitizeInputs = [
  body('*').optional().trim().escape(),
  query('*').optional().trim().escape(),
];

/**
 * User validation rules
 */
const validateUserRegistration = [
  body('firstName')
    .notEmpty()
    .withMessage('First name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('First name can only contain letters and spaces'),
    
  body('lastName')
    .notEmpty()
    .withMessage('Last name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Last name can only contain letters and spaces'),
    
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
    .isLength({ max: 255 })
    .withMessage('Email must not exceed 255 characters'),
    
  body('mobile')
    .notEmpty()
    .withMessage('Mobile number is required')
    .matches(/^(\+91|91|0)?[6789]\d{9}$|^\+1[2-9]\d{2}[2-9]\d{2}\d{4}$/)
    .withMessage('Please provide a valid mobile number (India: +91 or US: +1)'),
    
  body('role')
    .optional()
    .isIn(['buyer', 'seller'])
    .withMessage('Role must be either buyer or seller'),
    
  handleValidationErrors
];

/**
 * Content validation rules
 */
const validateContentCreation = [
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ min: 5, max: 200 })
    .withMessage('Title must be between 5 and 200 characters'),
    
  body('description')
    .notEmpty()
    .withMessage('Description is required')
    .isLength({ min: 20, max: 2000 })
    .withMessage('Description must be between 20 and 2000 characters'),
    
  body('sport')
    .notEmpty()
    .withMessage('Sport is required')
    .isIn(['Football', 'Basketball', 'Baseball', 'Soccer', 'Tennis', 'Golf', 'Hockey', 'Other'])
    .withMessage('Please select a valid sport'),
    
  body('contentType')
    .notEmpty()
    .withMessage('Content type is required')
    .isIn(['Strategy', 'Training', 'Analysis', 'Tutorial'])
    .withMessage('Please select a valid content type'),
    
  body('pricing.saleType')
    .notEmpty()
    .withMessage('Sale type is required')
    .isIn(['Fixed', 'Auction'])
    .withMessage('Sale type must be either Fixed or Auction'),
    
  body('pricing.price')
    .optional()
    .isFloat({ min: 1, max: 10000 })
    .withMessage('Price must be between $1 and $10,000'),
    
  body('pricing.startingBid')
    .optional()
    .isFloat({ min: 1, max: 10000 })
    .withMessage('Starting bid must be between $1 and $10,000'),
    
  handleValidationErrors
];

/**
 * Bid validation rules
 */
const validateBidCreation = [
  body('amount')
    .notEmpty()
    .withMessage('Bid amount is required')
    .isFloat({ min: 1, max: 50000 })
    .withMessage('Bid amount must be between $1 and $50,000'),
    
  param('contentId')
    .isMongoId()
    .withMessage('Invalid content ID'),
    
  handleValidationErrors
];

/**
 * Offer validation rules
 */
const validateOfferCreation = [
  body('amount')
    .notEmpty()
    .withMessage('Offer amount is required')
    .isFloat({ min: 1, max: 50000 })
    .withMessage('Offer amount must be between $1 and $50,000'),
    
  body('message')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Message must not exceed 500 characters'),
    
  param('contentId')
    .isMongoId()
    .withMessage('Invalid content ID'),
    
  handleValidationErrors
];

/**
 * Payment validation rules
 */
const validatePaymentCreation = [
  body('paymentMethodId')
    .notEmpty()
    .withMessage('Payment method is required')
    .isString()
    .withMessage('Payment method must be a string'),
    
  param('orderId')
    .isMongoId()
    .withMessage('Invalid order ID'),
    
  handleValidationErrors
];

/**
 * Review validation rules
 */
const validateReviewCreation = [
  body('rating')
    .notEmpty()
    .withMessage('Rating is required')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
    
  body('comment')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Comment must not exceed 1000 characters'),
    
  param('contentId')
    .isMongoId()
    .withMessage('Invalid content ID'),
    
  handleValidationErrors
];

/**
 * Admin settings validation rules
 */
const validateAdminSettings = [
  body('general.siteName')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Site name must be between 1 and 100 characters'),
    
  body('general.contactEmail')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid contact email'),
    
  body('general.contactPhone')
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Please provide a valid phone number'),
    
  body('launch.launchDateTime')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid date and time'),
    
  body('launch.maintenanceMode')
    .optional()
    .isBoolean()
    .withMessage('Maintenance mode must be true or false'),
    
  body('financial.platformCommissionPercentage')
    .optional()
    .isFloat({ min: 0, max: 50 })
    .withMessage('Platform commission must be between 0% and 50%'),
    
  handleValidationErrors
];

/**
 * Pagination validation
 */
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Page must be between 1 and 1000'),
    
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
    
  handleValidationErrors
];

/**
 * MongoDB ObjectId validation
 */
const validateObjectId = (paramName = 'id') => [
  param(paramName)
    .isMongoId()
    .withMessage(`Invalid ${paramName}`),
    
  handleValidationErrors
];

/**
 * File upload validation
 */
const validateFileUpload = (allowedTypes = [], maxSize = 50 * 1024 * 1024) => {
  return (req, res, next) => {
    if (!req.file) {
      return next(new ErrorResponse('No file uploaded', 400));
    }
    
    // Check file type
    if (allowedTypes.length > 0 && !allowedTypes.includes(req.file.mimetype)) {
      return next(new ErrorResponse(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`, 400));
    }
    
    // Check file size
    if (req.file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      return next(new ErrorResponse(`File size too large. Maximum size: ${maxSizeMB}MB`, 400));
    }
    
    next();
  };
};

module.exports = {
  sanitizeInputs,
  handleValidationErrors,
  validateUserRegistration,
  validateContentCreation,
  validateBidCreation,
  validateOfferCreation,
  validatePaymentCreation,
  validateReviewCreation,
  validateAdminSettings,
  validatePagination,
  validateObjectId,
  validateFileUpload
};
