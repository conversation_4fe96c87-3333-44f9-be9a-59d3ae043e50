## [4.16.5](https://github.com/OptimalBits/bull/compare/v4.16.4...v4.16.5) (2024-12-18)


### Bug Fixes

* upgrade cron-parser dependency for Luxon CVE-2023-22467 ([e45698e](https://github.com/OptimalBits/bull/commit/e45698eb91b91a4676f9bafdb7bdb35043d2316d))

## [4.16.4](https://github.com/OptimalBits/bull/compare/v4.16.3...v4.16.4) (2024-11-01)


### Bug Fixes

* **deps:** bump msgpackr to 1.1.2 to resolve ERR_BUFFER_OUT_OF_BOUNDS error ([#2783](https://github.com/OptimalBits/bull/issues/2783)) fixes [#2782](https://github.com/OptimalBits/bull/issues/2782) ([bc0ae0a](https://github.com/OptimalBits/bull/commit/bc0ae0ae5b7fb2b067de94c3e51ce1bdd609806d))

## [4.16.3](https://github.com/OptimalBits/bull/compare/v4.16.2...v4.16.3) (2024-09-10)


### Bug Fixes

* **metrics:** differentiate points in different minutes to be more accurate ([#2770](https://github.com/OptimalBits/bull/issues/2770)) ([fbf2fa3](https://github.com/OptimalBits/bull/commit/fbf2fa397c865e6620aac39b291704e486f424d2))

## [4.16.2](https://github.com/OptimalBits/bull/compare/v4.16.1...v4.16.2) (2024-09-05)


### Performance Improvements

* **metrics:** save zeros as much as max data points ([#2767](https://github.com/OptimalBits/bull/issues/2767)) ([3a09840](https://github.com/OptimalBits/bull/commit/3a098406a86573150ad13d1487d16211030c4200))

## [4.16.1](https://github.com/OptimalBits/bull/compare/v4.16.0...v4.16.1) (2024-08-28)


### Bug Fixes

* **metrics:** use batches include when collecting metrics ([#2765](https://github.com/OptimalBits/bull/issues/2765)) fixes [#2764](https://github.com/OptimalBits/bull/issues/2764) [#2763](https://github.com/OptimalBits/bull/issues/2763) ([8276f72](https://github.com/OptimalBits/bull/commit/8276f72b30a7abc2f3fb5509f980d931027d0f0c))

# [4.16.0](https://github.com/OptimalBits/bull/compare/v4.15.1...v4.16.0) (2024-07-30)


### Features

* **job:** support debouncing ([#2760](https://github.com/OptimalBits/bull/issues/2760)) ([603befe](https://github.com/OptimalBits/bull/commit/603befe439fb4e318137b88550ab4d0c6c2bbcb3))

## [4.15.1](https://github.com/OptimalBits/bull/compare/v4.15.0...v4.15.1) (2024-07-04)


### Bug Fixes

* **job:** check jobKey when saving stacktrace ([#2755](https://github.com/OptimalBits/bull/issues/2755)) ([96675f5](https://github.com/OptimalBits/bull/commit/96675f51d1a9e5a30df0a0fe24a40a2d14078203))

# [4.15.0](https://github.com/OptimalBits/bull/compare/v4.14.0...v4.15.0) (2024-06-30)


### Features

* **queue:** emit internal duplicated event ([#2754](https://github.com/OptimalBits/bull/issues/2754)) ([021ab7f](https://github.com/OptimalBits/bull/commit/021ab7fd8d6c89b01767572b0e53422f34f7bc04))

# [4.14.0](https://github.com/OptimalBits/bull/compare/v4.13.1...v4.14.0) (2024-06-25)


### Features

* **queue:** add global:duplicated event when a duplicated is added ([#2749](https://github.com/OptimalBits/bull/issues/2749)) ([d632ac1](https://github.com/OptimalBits/bull/commit/d632ac1010d7d7faaa8f731eafdea67d6129e111))

## [4.13.1](https://github.com/OptimalBits/bull/compare/v4.13.0...v4.13.1) (2024-06-21)


### Bug Fixes

* **priority:** consider paused state when calling getCountsPerPriority ([#2748](https://github.com/OptimalBits/bull/issues/2748)) ([6c2719a](https://github.com/OptimalBits/bull/commit/6c2719a9292bf0a11136d575d333600be0b9a422))

# [4.13.0](https://github.com/OptimalBits/bull/compare/v4.12.9...v4.13.0) (2024-06-12)


### Features

* **queue:** add getCountsPerPriority method ([#2746](https://github.com/OptimalBits/bull/issues/2746)) ([0376dcc](https://github.com/OptimalBits/bull/commit/0376dcc128d9af13ecbd658d8ea7ff19fce56915))

## [4.12.9](https://github.com/OptimalBits/bull/compare/v4.12.8...v4.12.9) (2024-05-24)


### Bug Fixes

* **retry-job:** throw error when job is not in active state ([#2741](https://github.com/OptimalBits/bull/issues/2741)) ([c29e3b0](https://github.com/OptimalBits/bull/commit/c29e3b061a168c3d40a336a733c382bab14caa57))

## [4.12.8](https://github.com/OptimalBits/bull/compare/v4.12.7...v4.12.8) (2024-05-22)


### Bug Fixes

* **move-to-finished:** throw error when job is not in active state ([#2739](https://github.com/OptimalBits/bull/issues/2739)) ([7b12be1](https://github.com/OptimalBits/bull/commit/7b12be13ea39e309d6a6a39a2e56f646f06fdfa8))

## [4.12.7](https://github.com/OptimalBits/bull/compare/v4.12.6...v4.12.7) (2024-05-21)


### Bug Fixes

* **scripts:** throw error when moving non-active job to delayed ([#2740](https://github.com/OptimalBits/bull/issues/2740)) ([63636b1](https://github.com/OptimalBits/bull/commit/63636b181d0a166b1702059e5abb53ce1589d90f))

## [4.12.6](https://github.com/OptimalBits/bull/compare/v4.12.5...v4.12.6) (2024-05-18)


### Bug Fixes

* **stalled:** take in count removeOnFail option ([#2734](https://github.com/OptimalBits/bull/issues/2734)) ([2112269](https://github.com/OptimalBits/bull/commit/21122697461b551055192ff3b9c02c6f37cb331e))

## [4.12.5](https://github.com/OptimalBits/bull/compare/v4.12.4...v4.12.5) (2024-05-17)


### Bug Fixes

* **job:** validate job existence when adding log ([#2738](https://github.com/OptimalBits/bull/issues/2738)) ([1fb1562](https://github.com/OptimalBits/bull/commit/1fb15628dc5912e83220d430aa14e19e90d1cc53))

## [4.12.4](https://github.com/OptimalBits/bull/compare/v4.12.3...v4.12.4) (2024-05-15)


### Bug Fixes

* **retry-job:** consider priority ([#2737](https://github.com/OptimalBits/bull/issues/2737)) fixes [#1755](https://github.com/OptimalBits/bull/issues/1755) ([09ce146](https://github.com/OptimalBits/bull/commit/09ce146563871519cda638bafa82ce6af34bdd25))

## [4.12.3](https://github.com/OptimalBits/bull/compare/v4.12.2...v4.12.3) (2024-05-10)


### Bug Fixes

* **job:** validate jobKey in updateProgress and update ([#2730](https://github.com/OptimalBits/bull/issues/2730)) ([6d84156](https://github.com/OptimalBits/bull/commit/6d8415696606d3b7ec891f7fca9ab0508923c321))


### Performance Improvements

* **scripts:** remove token after moving to wait or delayed ([#2731](https://github.com/OptimalBits/bull/issues/2731)) ([7ee8f74](https://github.com/OptimalBits/bull/commit/7ee8f7430a68492c9ce768e7108443592f49d74c))

## [4.12.2](https://github.com/OptimalBits/bull/compare/v4.12.1...v4.12.2) (2024-01-17)


### Bug Fixes

* **dependencies:** upgrade msgpackr ([cc83ae2](https://github.com/OptimalBits/bull/commit/cc83ae297e96344f94039401be5097d7f05ab10b))

## [4.12.1](https://github.com/OptimalBits/bull/compare/v4.12.0...v4.12.1) (2024-01-15)


### Bug Fixes

* **deps:** bump msgpackr from 1.5.2 to 1.10.1 ([#2697](https://github.com/OptimalBits/bull/issues/2697)) ([b27c90d](https://github.com/OptimalBits/bull/commit/b27c90d8b106cc5319e712df2386ddc35946ec3d))

# [4.12.0](https://github.com/OptimalBits/bull/compare/v4.11.5...v4.12.0) (2023-12-18)


### Features

* add missing extendLock definition ([14432ff](https://github.com/OptimalBits/bull/commit/14432ff8a5e743d97c82a360c9d6a92204b1a684))

## [4.11.5](https://github.com/OptimalBits/bull/compare/v4.11.4...v4.11.5) (2023-11-11)


### Bug Fixes

* pass redis string as opts into queue ([e94f568](https://github.com/OptimalBits/bull/commit/e94f568085de079fc42b876233a060ba11ec946e))

## [4.11.4](https://github.com/OptimalBits/bull/compare/v4.11.3...v4.11.4) (2023-10-14)


### Bug Fixes

* catch pause errors when closing ([ccb6cc7](https://github.com/OptimalBits/bull/commit/ccb6cc7ecca8c726fc14509536831f665ac49701))

## [4.11.3](https://github.com/OptimalBits/bull/compare/v4.11.2...v4.11.3) (2023-08-11)


### Bug Fixes

* **types:** make repeat option key optional ([934ec98](https://github.com/OptimalBits/bull/commit/934ec9875c6e04c3e771b6c6ba212e3693d3a25b))

## [4.11.2](https://github.com/OptimalBits/bull/compare/v4.11.1...v4.11.2) (2023-08-08)


### Bug Fixes

* **worker:** better client name support ([5910f44](https://github.com/OptimalBits/bull/commit/5910f44b03a264d979c8ade54d64d13fdc908b51))

## [4.11.1](https://github.com/OptimalBits/bull/compare/v4.11.0...v4.11.1) (2023-08-08)


### Bug Fixes

* **queue:** deep clone opts ([#2634](https://github.com/OptimalBits/bull/issues/2634)) fixes [#2633](https://github.com/OptimalBits/bull/issues/2633) ([35f1da3](https://github.com/OptimalBits/bull/commit/35f1da3cf631bee97e96a774d9f1127466e7a66a))

# [4.11.0](https://github.com/OptimalBits/bull/compare/v4.10.4...v4.11.0) (2023-08-08)


### Bug Fixes

* add mising getMetrics type ([#2640](https://github.com/OptimalBits/bull/issues/2640)) ([a217a7d](https://github.com/OptimalBits/bull/commit/a217a7d56d52385eb56ffe386b7503eca9a24604))
* remove deprecated debuglog ([4ce36fe](https://github.com/OptimalBits/bull/commit/4ce36febe3a63a45198e2fe24b46fc371ee3f6e5))
* **types:** add missing keys to repeat opts ([e4e6457](https://github.com/OptimalBits/bull/commit/e4e64572a3ad259d9cb90d5dec81e8565eeadca1))
* **types:** rename strategyOptions to options to reflect js file ([bae6427](https://github.com/OptimalBits/bull/commit/bae6427ce9d9fac26b198402068bd84647fd8208))
* **typings:** return type of getJobCountByTypes ([#2622](https://github.com/OptimalBits/bull/issues/2622)) ([47722ed](https://github.com/OptimalBits/bull/commit/47722ed791429b087128ce5f35847663b2d8fc9c))
* **worker:** high-memory-usage-when-providing-float-to-concurrency ([#2620](https://github.com/OptimalBits/bull/issues/2620)) ([dcca1e8](https://github.com/OptimalBits/bull/commit/dcca1e8c39b121fb01ac299bec30a3d011059c1f))
* change option name to match ts declaration ([909a07e](https://github.com/OptimalBits/bull/commit/909a07e27075a63b9ca178a3074b0b5c80d86355))
* ts declaration metrics option and getMetrics function ([11331b7](https://github.com/OptimalBits/bull/commit/11331b718a8e534ac6822917a536eab32b10446b))


### Features

* upgrade ioredis to 5.3.2 ([e1883f0](https://github.com/OptimalBits/bull/commit/e1883f01c2cb23a51b5485ef8048c4268ee968ea))

## [4.10.4](https://github.com/OptimalBits/bull/compare/v4.10.3...v4.10.4) (2023-02-09)


### Bug Fixes

* **retry:** handle pause queue status ([9f945d6](https://github.com/OptimalBits/bull/commit/9f945d60c69e8b5d7b46f58189a1c49a83897099))

## [4.10.3](https://github.com/OptimalBits/bull/compare/v4.10.2...v4.10.3) (2023-02-03)


### Bug Fixes

* don't reschedule delay timer if closing ([#2535](https://github.com/OptimalBits/bull/issues/2535)) ([8a0292a](https://github.com/OptimalBits/bull/commit/8a0292a574df82a62d718e13d8995800fd8529d0))

## [4.10.2](https://github.com/OptimalBits/bull/compare/v4.10.1...v4.10.2) (2022-11-24)


### Bug Fixes

* **queue:** throw error when needed instead of hiding it in a closure ([8a742c1](https://github.com/OptimalBits/bull/commit/8a742c1176e7147e2069602f18089d8becb4cb15))

## [4.10.1](https://github.com/OptimalBits/bull/compare/v4.10.0...v4.10.1) (2022-10-13)


### Bug Fixes

* support for instantiation using redisUrl ([6288f7d](https://github.com/OptimalBits/bull/commit/6288f7de9b82e712e480510eb10c03bd4d1cd24e))

# [4.10.0](https://github.com/OptimalBits/bull/compare/v4.9.0...v4.10.0) (2022-09-29)


### Features

* **types:** add typescript types to package ([e793f8d](https://github.com/OptimalBits/bull/commit/e793f8d1502bc5ed4a2e15087dc048c18e5e8644))

# [4.9.0](https://github.com/OptimalBits/bull/compare/v4.8.5...v4.9.0) (2022-09-05)


### Features

* support .cjs files ([75e6775](https://github.com/OptimalBits/bull/commit/75e6775fc3720563aac9cd9a07d1722dfbdfa177))

## [4.8.5](https://github.com/OptimalBits/bull/compare/v4.8.4...v4.8.5) (2022-07-27)


### Performance Improvements

* **clean:** use ZRANGEBYSCORE to improve performance ([#2363](https://github.com/OptimalBits/bull/issues/2363)) ([3331188](https://github.com/OptimalBits/bull/commit/3331188bce510e0bb4749d92cb63f4c73203d076))

## [4.8.4](https://github.com/OptimalBits/bull/compare/v4.8.3...v4.8.4) (2022-06-16)


### Bug Fixes

* **worker:** better disconnect when blocking connection ([b9ea7f4](https://github.com/OptimalBits/bull/commit/b9ea7f4780948d4556548e6bf13e2c3271939d12))

## [4.8.3](https://github.com/OptimalBits/bull/compare/v4.8.2...v4.8.3) (2022-05-12)


### Bug Fixes

* **stalled-jobs:** move stalled jobs to wait in batches ([2f1fb6c](https://github.com/OptimalBits/bull/commit/2f1fb6cdc1329f98b2dd30e847b5a79839db0346))

## [4.8.2](https://github.com/OptimalBits/bull/compare/v4.8.1...v4.8.2) (2022-04-21)


### Bug Fixes

* unlock job when moving it to delayed ([#2329](https://github.com/OptimalBits/bull/issues/2329)) ([11eae6b](https://github.com/OptimalBits/bull/commit/11eae6b960c83b47dadb22991b2b3e239c177508))

## [4.8.1](https://github.com/OptimalBits/bull/compare/v4.8.0...v4.8.1) (2022-03-21)


### Performance Improvements

* speed up clean operation ([#2326](https://github.com/OptimalBits/bull/issues/2326)) ([ef5f471](https://github.com/OptimalBits/bull/commit/ef5f4717258940042fa11f980622034cde765860))

# [4.8.0](https://github.com/OptimalBits/bull/compare/v4.7.0...v4.8.0) (2022-03-19)


### Features

* have Queue#clean consult job.{finishedOn,processedOn,timestamp} ([#2309](https://github.com/OptimalBits/bull/issues/2309)) ([b7058e6](https://github.com/OptimalBits/bull/commit/b7058e6e8f4bb56febebaf9452f993883240fa9d))

# [4.7.0](https://github.com/OptimalBits/bull/compare/v4.6.2...v4.7.0) (2022-03-02)


### Features

* **metrics:** add support for collecting queue metrics ([886d764](https://github.com/OptimalBits/bull/commit/886d7643819dcf52902d2e92394267dbd495c71b))

## [4.6.2](https://github.com/OptimalBits/bull/compare/v4.6.1...v4.6.2) (2022-02-23)


### Bug Fixes

* better handling of maxRetriesPerRequest ([d3b9138](https://github.com/OptimalBits/bull/commit/d3b91386e30d7205efdc19bcd18fe1e5fefa3542))

## [4.6.1](https://github.com/OptimalBits/bull/compare/v4.6.0...v4.6.1) (2022-02-21)


### Bug Fixes

* **sandbox:** better error reporting broken processor file ([10db479](https://github.com/OptimalBits/bull/commit/10db479731bcb8ba27c3a0e2dd4094c8e9ff1c57))

# [4.6.0](https://github.com/OptimalBits/bull/compare/v4.5.6...v4.6.0) (2022-02-21)


### Features

* handle redis uri queries ([54e5463](https://github.com/OptimalBits/bull/commit/54e5463bffee1b8b56b460460c79d8751142d859))

## [4.5.6](https://github.com/OptimalBits/bull/compare/v4.5.5...v4.5.6) (2022-02-20)


### Bug Fixes

* **sandbox:** wait for result of sending start command ([232ed85](https://github.com/OptimalBits/bull/commit/232ed85d4c980d94dee5d9e4c5b6f8758dbb82d9))

## [4.5.5](https://github.com/OptimalBits/bull/compare/v4.5.4...v4.5.5) (2022-02-16)


### Bug Fixes

* **worker:** better closing when disconnected ([41b9404](https://github.com/OptimalBits/bull/commit/41b940457b3447619c3c2887674a8cebf1508b07))

## [4.5.4](https://github.com/OptimalBits/bull/compare/v4.5.3...v4.5.4) (2022-02-14)


### Bug Fixes

* **queue:** return correct workers with getWorkers() ([193644c](https://github.com/OptimalBits/bull/commit/193644c5ed290901448f8c35ede99e1063a90f4a))

## [4.5.3](https://github.com/OptimalBits/bull/compare/v4.5.2...v4.5.3) (2022-02-14)


### Bug Fixes

* **commands:** do not wait for redis to load commands ([ad7b647](https://github.com/OptimalBits/bull/commit/ad7b6474db426b4970a0d1d3ddb8a032a22c481b))

## [4.5.2](https://github.com/OptimalBits/bull/compare/v4.5.1...v4.5.2) (2022-02-14)


### Bug Fixes

* **scripts:** make it easier for tools like vercel to find the .lua scripts ([8ab5b1d](https://github.com/OptimalBits/bull/commit/8ab5b1d1c3eecf41b20d97f464030377ece01640))

## [4.5.1](https://github.com/OptimalBits/bull/compare/v4.5.0...v4.5.1) (2022-02-06)


### Bug Fixes

* **sandbox:** broken processor files should fail jobs ([dd0b853](https://github.com/OptimalBits/bull/commit/dd0b853a516c32baac535395377a19e361964dc8))

# [4.5.0](https://github.com/OptimalBits/bull/compare/v4.4.0...v4.5.0) (2022-02-01)


### Features

* **queue:** add retryJobs for failed status ([501b2cc](https://github.com/OptimalBits/bull/commit/501b2cc49ccd0d7de82fea50144b52fa9879d1f5))

# [4.4.0](https://github.com/OptimalBits/bull/compare/v4.3.0...v4.4.0) (2022-01-26)


### Features

* add support for removeOn based on time ([90f040c](https://github.com/OptimalBits/bull/commit/90f040c052325da302c99f17111a12d1afbe88bd))

# [4.3.0](https://github.com/OptimalBits/bull/compare/v4.2.1...v4.3.0) (2022-01-26)


### Features

* upgrade cron version enabling new cron expressions ([79337a3](https://github.com/OptimalBits/bull/commit/79337a30758ea7e6d6b1536eb0edebcd0b3c8274))

## [4.2.1](https://github.com/OptimalBits/bull/compare/v4.2.0...v4.2.1) (2022-01-17)


### Bug Fixes

* **sandbox:** exit if uncaughtException ([43dc2e6](https://github.com/OptimalBits/bull/commit/43dc2e69dfa0cbaf960ce6f1bd6c4125b1052ff9))

# [4.2.0](https://github.com/OptimalBits/bull/compare/v4.1.4...v4.2.0) (2021-12-21)


### Features

* **queue:** enabled queues to share childPool instance ([#2237](https://github.com/OptimalBits/bull/issues/2237)) ([16fdbe9](https://github.com/OptimalBits/bull/commit/16fdbe90a05ae2e2bcb134a550a97dc84dfe573e))

## [4.1.4](https://github.com/OptimalBits/bull/compare/v4.1.3...v4.1.4) (2021-12-14)


### Bug Fixes

* **queue:** check redisOptions is available fixes [#2186](https://github.com/OptimalBits/bull/issues/2186) ([071c51d](https://github.com/OptimalBits/bull/commit/071c51d16db58ed9f71138058dbbc25f73383e56))

## [4.1.3](https://github.com/OptimalBits/bull/compare/v4.1.2...v4.1.3) (2021-12-14)


### Bug Fixes

* typo on url ([#2195](https://github.com/OptimalBits/bull/issues/2195)) ([7e7d9cb](https://github.com/OptimalBits/bull/commit/7e7d9cb58d7cb5c1a92020f7c4333932526d8e98))

## [4.1.2](https://github.com/OptimalBits/bull/compare/v4.1.1...v4.1.2) (2021-12-14)


### Performance Improvements

* speed up performance of queue.clean when called with a limit ([#2205](https://github.com/OptimalBits/bull/issues/2205)) ([c20e469](https://github.com/OptimalBits/bull/commit/c20e469dcd71fd13e23e922f2720f55450311d22))

## [4.1.1](https://github.com/OptimalBits/bull/compare/v4.1.0...v4.1.1) (2021-11-16)


### Bug Fixes

* **emit:** protect emit calls fixes [#2213](https://github.com/OptimalBits/bull/issues/2213) ([4978a2b](https://github.com/OptimalBits/bull/commit/4978a2b40ee840ba91e0939e86e1e3b15e8b16e9))

# [4.1.0](https://github.com/OptimalBits/bull/compare/v4.0.0...v4.1.0) (2021-10-31)


### Features

* emit event on job lock extend failure ([7247b3b](https://github.com/OptimalBits/bull/commit/7247b3bb9741c5eb18ce4027ea14349cbc8504c5))

# [4.0.0](https://github.com/OptimalBits/bull/compare/v3.29.3...v4.0.0) (2021-10-27)


### Bug Fixes

* force options to guarantee correct reconnects ([3ade8e6](https://github.com/OptimalBits/bull/commit/3ade8e6727d7b906a30b09bccb6dc10d76ed1b5f))


### BREAKING CHANGES

* If redis opts are missing:
 { maxRetriesPerRequest: null,
   enableReadyCheck: false }
then a exception will be thrown.

## [3.29.3](https://github.com/OptimalBits/bull/compare/v3.29.2...v3.29.3) (2021-10-13)


### Bug Fixes

* **name-processors:** wait for all processors when closing fixes [#1618](https://github.com/OptimalBits/bull/issues/1618) ([79ce013](https://github.com/OptimalBits/bull/commit/79ce013af695f96ff57106b213982647e0783d3f))

## [3.29.2](https://github.com/OptimalBits/bull/compare/v3.29.1...v3.29.2) (2021-09-08)


### Bug Fixes

* **connection:** fail only if redis connection does not recover ([0ca4c6b](https://github.com/OptimalBits/bull/commit/0ca4c6b4d57efa78e5ca484cb8bed2a6961646a3))

## [3.29.1](https://github.com/OptimalBits/bull/compare/v3.29.0...v3.29.1) (2021-08-26)


### Bug Fixes

* protect getJob with isReady, fixes [#1386](https://github.com/OptimalBits/bull/issues/1386) ([2f27faa](https://github.com/OptimalBits/bull/commit/2f27faa410f70504f24df2ebd6bb7831df21660d))

# [3.29.0](https://github.com/OptimalBits/bull/compare/v3.28.1...v3.29.0) (2021-08-20)


### Features

* **jobs:** add extendLock method ([30d5959](https://github.com/OptimalBits/bull/commit/30d59590c34cb664e3b9a62695c4092c9b1ae3f3))

## [3.28.1](https://github.com/OptimalBits/bull/compare/v3.28.0...v3.28.1) (2021-08-06)


### Bug Fixes

* **queue:** changed user prop to username for redisOptions ([71baea9](https://github.com/OptimalBits/bull/commit/71baea91de702d9bd1d5516f09b22599b7f13045))

# [3.28.0](https://github.com/OptimalBits/bull/compare/v3.27.0...v3.28.0) (2021-08-05)


### Features

* **queue:** handle redis url containing username ([a245fc4](https://github.com/OptimalBits/bull/commit/a245fc403827fd22b08dbe1499fe843eb1ad633a))

# [3.27.0](https://github.com/OptimalBits/bull/compare/v3.26.0...v3.27.0) (2021-07-27)


### Features

* support job.discard function in sandboxed processors ([5adcf2c](https://github.com/OptimalBits/bull/commit/5adcf2ceed263d46089cceefcdcaa658151c53b7))

# [3.26.0](https://github.com/OptimalBits/bull/compare/v3.25.2...v3.26.0) (2021-07-16)


### Features

* **repeatable:** store key in repeat options ([dab0d82](https://github.com/OptimalBits/bull/commit/dab0d8266174f1d25ec914cff8450594d85db511))

## [3.25.2](https://github.com/OptimalBits/bull/compare/v3.25.1...v3.25.2) (2021-07-16)


### Bug Fixes

* **repeatable:** honor endDate fixes [#1573](https://github.com/OptimalBits/bull/issues/1573) ([7f0db0e](https://github.com/OptimalBits/bull/commit/7f0db0e293367752971be8a1bf7b6c8cf4190350))

## [3.25.1](https://github.com/OptimalBits/bull/compare/v3.25.0...v3.25.1) (2021-07-16)


### Bug Fixes

* error when .lua scripts missing in built bundle ([85307c3](https://github.com/OptimalBits/bull/commit/85307c3a8e48b910590b2682700c5062c84d32b0))

# [3.25.0](https://github.com/OptimalBits/bull/compare/v3.24.0...v3.25.0) (2021-07-15)


### Features

* pass clientName to createClient function ([2a29569](https://github.com/OptimalBits/bull/commit/2a295691b88318ff64beaa7b83e03487854a7fe4))

# [3.24.0](https://github.com/OptimalBits/bull/compare/v3.23.3...v3.24.0) (2021-07-15)


### Features

* **backoff:** add option to specify options for custom backoff strategy ([e573010](https://github.com/OptimalBits/bull/commit/e5730107688a41c7268a717f16302c5959eaf6f6))

## [3.23.3](https://github.com/OptimalBits/bull/compare/v3.23.2...v3.23.3) (2021-07-15)


### Bug Fixes

* **delayed:** do not get lock twice fixes [#2033](https://github.com/OptimalBits/bull/issues/2033) ([2800cf8](https://github.com/OptimalBits/bull/commit/2800cf8923ccac52dcd4da4e7fd19b3404c68fe8))

## [3.23.2](https://github.com/OptimalBits/bull/compare/v3.23.1...v3.23.2) (2021-07-15)


### Bug Fixes

* **job:** add default err object fixes [#2029](https://github.com/OptimalBits/bull/issues/2029) ([39684e9](https://github.com/OptimalBits/bull/commit/39684e9f941e2ea4191809fdc2aa52b3d7b267ae))

## [3.23.1](https://github.com/OptimalBits/bull/compare/v3.23.0...v3.23.1) (2021-07-15)


### Bug Fixes

* wait in queue to be ready in getNextJob fixes [#1852](https://github.com/OptimalBits/bull/issues/1852) ([4e224e5](https://github.com/OptimalBits/bull/commit/4e224e5533f729b9781b1db81e1875b1bd50afb0))

# [3.23.0](https://github.com/OptimalBits/bull/compare/v3.22.12...v3.23.0) (2021-07-13)


### Features

* support job.update function in sandboxed processors ([ff79fb4](https://github.com/OptimalBits/bull/commit/ff79fb494ea3e123256b35d18b46b24fbb9b8365)), closes [#1279](https://github.com/OptimalBits/bull/issues/1279) [#1608](https://github.com/OptimalBits/bull/issues/1608) [#1056](https://github.com/OptimalBits/bull/issues/1056)

## [3.22.12](https://github.com/OptimalBits/bull/compare/v3.22.11...v3.22.12) (2021-07-13)


### Bug Fixes

* remove stalled job when finishing fixes [#1600](https://github.com/OptimalBits/bull/issues/1600) ([90763fd](https://github.com/OptimalBits/bull/commit/90763fd66404d8bc5a47ff8555cbe2da776c030d))

## [3.22.11](https://github.com/OptimalBits/bull/compare/v3.22.10...v3.22.11) (2021-07-08)


### Bug Fixes

* **close:** clear timers after waiting jobs fixes [#1415](https://github.com/OptimalBits/bull/issues/1415) ([77b319d](https://github.com/OptimalBits/bull/commit/77b319da6acaa93351830f0a4e38b5ad1d3d8cf5))

## [3.22.10](https://github.com/OptimalBits/bull/compare/v3.22.9...v3.22.10) (2021-07-01)


### Bug Fixes

* deep clone options ([#2083](https://github.com/OptimalBits/bull/issues/2083)) ([1e00a90](https://github.com/OptimalBits/bull/commit/1e00a90d1216083cdf33bc78a9753ece1acdddb2))

## [3.22.9](https://github.com/OptimalBits/bull/compare/v3.22.8...v3.22.9) (2021-06-22)


### Bug Fixes

* **reprocess:** do not store job.id in added list ([3fbc506](https://github.com/OptimalBits/bull/commit/3fbc506aee4c36fd612ed18fc3a1619c0c8069d8))

## [3.22.8](https://github.com/OptimalBits/bull/compare/v3.22.7...v3.22.8) (2021-06-09)


### Bug Fixes

* upgrade ioredis fixes [#1445](https://github.com/OptimalBits/bull/issues/1445) ([f6a2364](https://github.com/OptimalBits/bull/commit/f6a23648146f5a53bb2e4084f8de9b783b782bed))

## [3.22.7](https://github.com/OptimalBits/bull/compare/v3.22.6...v3.22.7) (2021-05-31)


### Bug Fixes

* **obliterate:** remove job logs fixes [#2050](https://github.com/OptimalBits/bull/issues/2050) ([6ccf2b9](https://github.com/OptimalBits/bull/commit/6ccf2b90892bf16b4edb0095d6e770f978bc660a))

## [3.22.6](https://github.com/OptimalBits/bull/compare/v3.22.5...v3.22.6) (2021-05-17)


### Bug Fixes

* **job:** fix job log pagination, fixes [#2031](https://github.com/OptimalBits/bull/issues/2031) ([30aa0a9](https://github.com/OptimalBits/bull/commit/30aa0a99acb4a04a12f988840bc8ccc7a014fed3))

## [3.22.5](https://github.com/OptimalBits/bull/compare/v3.22.4...v3.22.5) (2021-05-11)


### Bug Fixes

* emit failed event when stalled job fails ([f68da41](https://github.com/OptimalBits/bull/commit/f68da4176658b9935cf4b63b218130008619b25b))

## [3.22.4](https://github.com/OptimalBits/bull/compare/v3.22.3...v3.22.4) (2021-04-27)


### Bug Fixes

* also populate retriedOn when loading from id with excludeData ([0964b39](https://github.com/OptimalBits/bull/commit/0964b390d9190510f0d016f4eb2a3f1bc6cdb4e5))

## [3.22.3](https://github.com/OptimalBits/bull/compare/v3.22.2...v3.22.3) (2021-04-23)


### Bug Fixes

* **delayed:** re-schedule updateDelay in case of error fixes [#2015](https://github.com/OptimalBits/bull/issues/2015) ([16bbfad](https://github.com/OptimalBits/bull/commit/16bbfadb270bc6c7d6df9cf5ab30b7f66028b2b3))

## [3.22.2](https://github.com/OptimalBits/bull/compare/v3.22.1...v3.22.2) (2021-04-23)


### Bug Fixes

* **obliterate:** obliterate many jobs fixes [#2016](https://github.com/OptimalBits/bull/issues/2016) ([7a923b4](https://github.com/OptimalBits/bull/commit/7a923b468d5299bbdfe06d1ee7447fd810e2779b))


## v.3.22.1

- fix(obliterate): remove repeatable jobs fixes #2012

[Changes](https://github.com/OptimalBits/bull/compare/v3.22.0...v3.22.1)

## v.3.22.0

- feat: do not rely on comma to encode jobid in progress fixes #2003

[Changes](https://github.com/OptimalBits/bull/compare/v3.21.1...v3.22.0)


## v.3.21.1

- fix: safer implementation of obliterate.
Note: If you want to use the new method "obliterate" it is highly recommended
that you upgrade to this version, since previous version is not safe to use
if using the colon ':' character in your queue names.


[Changes](https://github.com/OptimalBits/bull/compare/v3.21.0...v3.21.1)

## v.3.21.0

- feat: add a method to "obliterate" a queue
- fix: upgrade lodash fixes #1996

[Changes](https://github.com/OptimalBits/bull/compare/v3.20.1...v3.21.0)

## v.3.20.1

- fix(queue): possible infinite loop when disconnect fixes #1746
- fix(clean-priority): remove job from priority set on clean (#1405)
- fix(sandbox): job update (#1957)
- fix: use async version of process.send for progress and log (#1948)
- fix: promote jobs to the right "list" when paused

[Changes](https://github.com/OptimalBits/bull/compare/v3.20.0...v3.20.1)

## v.3.20.0

- feat(job): implement Job#retriedOn (#1868)
- fix: job default opts fixes #1904

[Changes](https://github.com/OptimalBits/bull/compare/v3.19.1...v3.20.0)

## v.3.19.1

- fix(getters): properly zip hmget result

[Changes](https://github.com/OptimalBits/bull/compare/v3.19.0...v3.19.1)

## v.3.19.0

- feat: add option to exclude data on getters (#1910)
- fix: lock ioredis to 4.18.0 to avoid breakage with newer 4.19.0+.

[Changes](https://github.com/OptimalBits/bull/compare/v3.18.1...v3.19.0)

## v.3.18.1

- fix(repeat): remove last delayed job.
- fix(rate-limiter): increment jobCounter only when a job is processed. fixes #1875.
- fix(sandbox): clear dangling timeout.

[Changes](https://github.com/OptimalBits/bull/compare/v3.18.0...v3.18.1)

## v.3.18.0

- feat: make pause forward compatible with bullmq (#1818) (@manast)

[Changes](https://github.com/OptimalBits/bull/compare/v3.17.0...v3.18.0)

## v.3.17.0

- feat: better rate limiter (#1816) (@manast)
- feat(sandbox): kill child workers gracefully (#1802) (@GabrielCastro)

[Changes](https://github.com/OptimalBits/bull/compare/v3.16.0...v3.17.0)

## v.3.16.0

- feat(rate-limiter): add grouping support.

[Changes](https://github.com/OptimalBits/bull/compare/v3.15.0...v3.16.0)

## v.3.15.0

- feat: add isPaused fixes #1274
- fix: emit waiting event when adding a priority job (#1134)

[Changes](https://github.com/OptimalBits/bull/compare/v3.14.0...v3.15.0)

## v.3.14.0

- feat(queue): add removeJobs function
- fix: clamp negative job delay values to 0 to prevent thrashing
- fix: use DEFAULT_JOB_NAME (#1585)
- fix: remove the lazy client error handler on close (#1605)
- fix: prevent exceeding the maximum stack call size when emptying large queues (#1660)

[Changes](https://github.com/OptimalBits/bull/compare/v3.13.0...v3.14.0)

## v.3.13.0

- feat: add "preventParsingData" job option to prevent data parsing
- fix: queue.clean clean job logs as well
- fix: whenCurrentJobsFinished should wait for all jobs

[Changes](https://github.com/OptimalBits/bull/compare/v3.12.1...v3.13.0)

## v.3.12.1

- fix: catch errors parsing invalid progress data
- fix(pause): don't initialize bclient if not waiting for jobs to finish

[Changes](https://github.com/OptimalBits/bull/compare/v3.12.0...v3.12.1)

## v.3.12.0

- feat: support async custom backoffs.
- feat(sandbox): emulate job.progress function.

- fix: queue.pause(true, true) doesn't pause queue.

[Changes](https://github.com/OptimalBits/bull/compare/v3.11.0...v3.12.0)

## v.3.11.0

- feat(queue): basic support for bulk adding jobs.
- feat(job): save data on job instance when updated.

- fix(queue): whenCurrentJobsFinished shouldn't initialize bclient. Fixes #1346.
- fix(queue): unhandled promise warning in updateDelayTimer.
- fix(sandbox): if the child process is killed, remove it from the pool.

[Changes](https://github.com/OptimalBits/bull/compare/v3.10.0...v3.11.0)

## v.3.10.0

- fix: remove logs automtically when using autoremove fixes #1330
- feat: add support for keeping a specified number of jobs when using auto-remove.
- feat: add support for node 12
- fix: fix check for supported file types in sandboxed processors #1311
- ci: drop support for node 6

[Changes](https://github.com/OptimalBits/bull/compare/v3.9.1...v3.10.0)

## v.3.9.1

- fix: add log to job wrapper

[Changes](https://github.com/OptimalBits/bull/compare/v3.9.0...v3.9.1)

## v.3.9.0

- feat: add job.log #1165

[Changes](https://github.com/OptimalBits/bull/compare/v3.8.1...v3.9.0)

## v.3.8.1

- fix: wait for ready in cleanJobsInSet fixes #1298

[Changes](https://github.com/OptimalBits/bull/compare/v3.8.0...v3.8.1)

## v.3.8.0

- fix: improve delay logic fixing #1226, #1222
- feat: store finishedOn on the job instance
- fix: return every in getRepeatableJobs #1284
- fix: remove broken child processes #1098
- feat: update sandbox exit handler to log signals #1252
- fix: Ignore unknown command client error #1240

[Changes](https://github.com/OptimalBits/bull/compare/v3.7.0...v3.8.0)

## v.3.7.0

- perf: improve update delay set logic.
- feat: consider priority when promoting a job #1205.
- fix: better delay for rate limited jobs.
- feat: update port selection mechanism for child node process inspect flag.

[Changes](https://github.com/OptimalBits/bull/compare/v3.6.0...v3.7.0)

## v.3.6.0

- feat: add function to remove repeatable jobs by key.
- fix: properly remove sandbox events. Fixes #1179.
- fix: make progress functions in sandbox consistently return a promise.

[Changes](https://github.com/OptimalBits/bull/compare/v3.5.3...v3.6.0)

## v.3.5.3

- chore: upgrade ioredis to ^4.5.1.
- fix: fix #1044 support for typescript processors.
- chore: remove bluebird.
- chore: use lockKey method instead of private property.
- fix(queue): convert delay setting to number.

[Changes](https://github.com/OptimalBits/bull/compare/v3.5.2...v3.5.3)

## v.3.5.2

- chore(queue): remove bluebird config from the codebase.

[Changes](https://github.com/OptimalBits/bull/compare/v3.5.1...v3.5.2)

## v.3.5.1

- chore(yarn): updated yarn.lock

[Changes](https://github.com/OptimalBits/bull/compare/v3.5.0...v3.5.1)

## v.3.5.0

- fix(delayed): pause delayed jobs #1087
- fix(lua): correct numJobs fetch in moveToActive
- perf(moveToActive): used local var for rate limiter
- perf(queue): replace bluebird by native promises where possible
- chore(queue): fix typo in forcedReconnection variable
- feat(child-processes): catch sub process crashes
- fix(jobs): reset 'failedReason', 'finishedOn' and 'processedOn' fields on job retry
- fix(queue): fix Warning: cancellation is disabled
- fix(queue): remove the correct listener in isRedisReady
- feat(jobs): allow cancelling of retries when using custom backoff strategy
- feat(rate-limiter): add discard config for rate-limiter
- feat(jobs): make job progress accepts variant types
- fix(repeatable): Fixed wrong repeatable count updates
- fix(jobs): fix copy paste mistake for stacktrace in job toData
- feat(child-processes): Propagate stack traces
- feat(repeatable): add ability for cron repeatable job with startDate

[Changes](https://github.com/OptimalBits/bull/compare/v3.4.8...v3.5.0)

## v.3.4.8

- emit waiting event when waking up sleep jobs fixing #792
- throw error if missing processor file fixing #954

[Changes](https://github.com/OptimalBits/bull/compare/v3.4.7...v3.4.8)

## v.3.4.7

- Fixes to deal with removing correctly in priority queues #984

[Changes](https://github.com/OptimalBits/bull/compare/v3.4.6...v3.4.7)

## v.3.4.6

- Reverted use of arrow function that was incompatible with older versions of node.

[Changes](https://github.com/OptimalBits/bull/compare/v3.4.5...v3.4.6)

## v.3.4.5

- Fixed Unhandled promise rejections #1012.

[Changes](https://github.com/OptimalBits/bull/compare/v3.4.4...v3.4.5)

## v.3.4.4

- Partially fixed #845. When call queue.close() bull throws Error: Connection is closed.
- Fixed #998. Check for existence of rate limiter options.
- Fixed #1003. Fixed fixed repeatable jobs duplication using every.
- Feature/provide error to custom backoff strategy.

[Changes](https://github.com/OptimalBits/bull/compare/v3.4.3...v3.4.4)

## v.3.4.3

- Fixed #994 queue.getJobs() race condition.
- Fixed #966 Race conditions reviving repeatable jobs.
- Fixed getters: Update types array to include paused.
- Fixed #958 job.finished slowdown.
- Fixed #949 TypeError: job.queue.client.isFinished is not a function.
- Fixed #870 TypeError when retrying jobs.
- Fixed #942 Support for milliseconds intervals in repeatable jobs.

[Changes](https://github.com/OptimalBits/bull/compare/v3.4.2...v3.4.3)

## v.3.4.2

- Fixed #903 Globally paused queue cannot receive job (or not shown in Arena untill queue is globally resumed).
- Workaround for #911 Seperate process worker fails to launch when Node is started with --inspect flag
- added missing retain on reused child job #908.
- added more tests for child jobs.

[Changes](https://github.com/OptimalBits/bull/compare/v3.4.1...v3.4.2)

## v.3.4.1

- Better check for closing in moveUnlockedJobsToWait, possibly fixing #806.

[Changes](https://github.com/OptimalBits/bull/compare/v3.4.0...v3.4.1)

## v.3.4.0

- Added support for prioritized delayed jobs.
- Added ability to process all named jobs from one process function.
- Fixed #893, warning 'a promise was rejected with a non-error' for external queues in case of an error.

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.10...v3.4.0)

## v.3.3.10

- Faster next job fetch #868
- Added global default options for jobs. Fixes #706.
- Added a limit for repeatable jobs. #854.

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.9...v3.3.10)

## v.3.3.9

- Support custom backoff strategies.
- Fixed #786. Handling of ES6 default export.
- Fixed #782. Better handling of "isReady".

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.8...v3.3.9)

## v.3.3.8

- Fixed #812. External process doesn't terminate on `queue.close()`.
- Fixed #830. Named Process Sent to Wrong Processor.
- Fixed #572. Do not close external connections.

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.7...v3.3.8)

## v.3.3.7

- Fixed #807.
- Adding ability to limit by stacktrace. #798.

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.6...v3.3.7)

## v.3.3.6

- Fixed #766, #781, #716.
- Correctly accept DB in redis connection string.
- Fixed global progress event.

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.5...v3.3.6)

## v.3.3.5

- Fixed #764, #762, #759.

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.4...v3.3.5)

## v.3.3.4

- Fixed #748.

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.3...v3.3.4)

## v.3.3.3

- Re-fixed #739.
- Possibly fixed for #747.
- Fixed removeRepeatable (missing file)

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.2...v3.3.3)

## v.3.3.2

- Fixed #721. SyntaxError: Unexpected token u in JSON at position 0.
- Fixed #739. childs are not added to the retained set.
- Fixed #734. fixed Promise warnings.

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.1...v3.3.2)

## v.3.3.1

- Fixed #714

[Changes](https://github.com/OptimalBits/bull/compare/v3.3.0...v3.3.1)

## v.3.3.0

- Added a method `Queue##removeRepeatable` to remove repeatable jobs.
- Now also emits drained as a global event.
- Fixed #518, #624

[Changes](https://github.com/OptimalBits/bull/compare/v3.2.0...v3.3.0)

## v.3.2.0

- Added support for running jobs in child processes #488

[Changes](https://github.com/OptimalBits/bull/compare/v3.1.0...v3.2.0)

## v.3.1.0

- Added rate limiter support.
- Added method to update jobs data.
- Implemented stalled as global event.

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0...v3.1.0)

## v.3.0.0

- No changes.

## v.3.0.0-rc.10

- Fixed #666.
- Small improvements in the repeat code.

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-rc.9...v3.0.0-rc.10)

## v.3.0.0-rc.9

- Fixed #672.
- Fixed #670

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-rc.8...v3.0.0-rc.9)

## v.3.0.0-rc.8

- Enhanced job fetching #651 (faster and more reliable).

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-rc.7...v3.0.0-rc.8)

## v.3.0.0-rc.7

- Fixed #659

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-rc.6...v3.0.0-rc.7)

## v.3.0.0-rc.6

- Fixed #645.

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-rc.5...v3.0.0-rc.6)

## v.3.0.0-rc.5

- Improved performance, specially when having many concurrent workers.
- Fixed #609 using zsets for storing repeatable jobs.
- Fixed #608 Event chaining no longer works.
- Improved getters.
- Fixed #601 Add multiple repeatable jobs with the same cron pattern.

[Changes](https://github.com/OptimalBits/bull/compare/3.0.0-rc.4...v3.0.0-rc.5)

## v.3.0.0-rc.4

- Added support for naming workers in redis connections #530.
- Lazy instantiation of redis clients. Fixes #526.
- job.finished captures result from queue process. #588.
- Caches LUA scripts to avoid reading files in every queue instantiation. #591.
- Emit 'drain' event when queue is empty. #596.
- store finished and processed timestamps. #594, #606.

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-rc.3...3.0.0-rc.4)

## v.3.0.0-rc.3

- Fixed #579.
- Lazy subscription to events for better performance.
- Corrected calculation of next repeat job. #563.

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-rc.2...v3.0.0-rc.3)

## v.3.0.0-rc.2

- Improved performance of moveToActive #550.
- Fixed issue with cancelable promise #546.

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-rc.1...v3.0.0-rc.2)

## v.3.0.0-rc.1

- Improved error and lock handling for failed jobs #499, #539.
- Corrected instantiation from urls #538.
- Return jobs in proper order in jobs getters.

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-alpha.4...v3.0.0-rc.1)

## v.3.0.0-alpha.4

- Implemented repeatable jobs. #252.

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-alpha.3...v3.0.0-alpha.4)

## v.3.0.0-alpha.3

- Simplified global events #501.

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-alpha.2...v3.0.0-alpha.3)

## v.3.0.0-alpha.2

- Eliminated possible memory leak #503

[Changes](https://github.com/OptimalBits/bull/compare/v3.0.0-alpha.1...v3.0.0-alpha.2)

## v.3.0.0-alpha.1

- improved job fetch mechanism. #480.
- job.jobId changed to job.id.
- refactored error messages into separate error module.
- refactored lua scripts into separate files, and preloaded.
- many atomizations and clean ups.
- completed and failed job states are now represented in ZSETs. #190.

[Changes](https://github.com/OptimalBits/bull/compare/v2.2.6...v3.0.0-alpha.1)

## v.2.2.6

- Persisted failedReason when storing job data.
- added queue##isReady()

[Changes](https://github.com/OptimalBits/bull/compare/v2.2.5...v2.2.6)

## v.2.2.5

- Fixed so that redis key prefix works properly.

[Changes](https://github.com/OptimalBits/bull/compare/v2.2.4...v2.2.5)

## v.2.2.4

- Allow reusing certain redis connections.

[Changes](https://github.com/OptimalBits/bull/compare/v2.2.3...v2.2.4)

## v.2.2.3

- Added getJobCounts.
- Fixed global events #394.

[Changes](https://github.com/OptimalBits/bull/compare/v2.2.2...v2.2.3)

## v.2.2.2

- Fixed redis script cache gets bloated after update to bull 2.0 #426

[Changes](https://github.com/OptimalBits/bull/compare/v2.2.1...v2.2.2)

## v.2.2.1

- Re-added createClient option that was removed by mistake.
- Corrected getJobCountByTypes, fixes #419 and #401

[Changes](https://github.com/OptimalBits/bull/compare/v2.2.0...v2.2.1)

## v.2.2.0

- Much improved priority queues, simpler, faster and more reliable.
- Fixed issue where lua scripts where leaking memory.
- Improvements in local pause, fixing #446 and #447.
- Fix to increase delay time over 24 days #244

[Changes](https://github.com/OptimalBits/bull/compare/v2.1.2...v2.2.0)

## v.2.1.2

- Fixed Error renewing lock LockError: Exceeded 0 attempts to lock the resource #437
- Fixed Unable to renew nonexisting lock on job fail #441

[Changes](https://github.com/OptimalBits/bull/compare/v2.1.1...v2.1.2)

## v.2.1.1

- Catch errors produced in timers. Related to #441

[Changes](https://github.com/OptimalBits/bull/compare/v2.1.0...v2.1.1)

## v.2.1.0

- Fixed #397, Error: Unable to renew nonexisting lock
- Fixed #402, Job.prototype.finished contains error in promise
- Fixed #371, "Unexpected token u in JSON at position 0" while processing job
- New feature #363, "method to permanently fail a job"
- Fix job.progress() to return the correct progress

[Changes](https://github.com/OptimalBits/bull/compare/v2.0.0...v2.1.0)

## v.2.0.0

- Changed redis module to ioredis fixing many issues along the way, see changes.

[Changes](https://github.com/OptimalBits/bull/compare/v1.1.3...v2.0.0)

## v.1.1.3

- fixed "Broken constructor pattern from recent commit" #384
- fixed "Queue.prototype.getWaiting() returns empty list if Queue is paused" #342

[Changes](https://github.com/OptimalBits/bull/compare/v1.1.2...v1.1.3)

## v1.1.2

- regained backwards compatibility in events by using disturbed 1.0.6

[Changes](https://github.com/OptimalBits/bull/compare/v1.1.1...v1.1.2)

## v1.1.1

- Returned this in queue##on and queue##once for backwards compatibility.
- [Fixes PriorityQueue Events and Local Worker Pause/Resume](https://github.com/OptimalBits/bull/pull/341)

[Changes](https://github.com/OptimalBits/bull/compare/v1.1.0...v1.1.1)

## v1.1.0

- Fixed [job corruption issue](https://github.com/OptimalBits/bull/pull/359)
- The job id can be [overridden](https://github.com/OptimalBits/bull/pull/335) to implement job throttling behavior
- Added [`removeOnComplete` job option](https://github.com/OptimalBits/bull/pull/361)
- [More robust job retry](https://github.com/OptimalBits/bull/pull/318)
- Events are [now broadcast to all workers](https://github.com/OptimalBits/bull/commit/d55ad1c8f44f86be9b4e9f4fa9a3fc8a16c6e02d)

[Changes](https://github.com/OptimalBits/bull/compare/v1.0.0...v1.1.0)

## v1.0.0

- improvements in clean (fixes and performance).

[Changes](https://github.com/OptimalBits/bull/compare/v1.0.0-rc4...v1.0.0)

## v1.0.0-rc4

- fixed lock renew logic.
- atomized code for getting stalled jobs.

[Changes](https://github.com/OptimalBits/bull/compare/v1.0.0-rc3...v1.0.0-rc4)

## v1.0.0-rc3

- smaller fixes.

[Changes](https://github.com/OptimalBits/bull/compare/v1.0.0-rc2...v1.0.0-rc3)

## v1.0.0-rc2

- Improved locking when removing and processing stalled jobs.
- Fixed #302 EVALSHA failure.
- Fixed #295 error with redis 3.2.
- Correctly allows the specification of the db
- Honor start/end range for complete/failed jobs.
- Fixed #277 Memory Leaks With Large Queue.
- Support for custom key prefix for redis keys.

[Changes](https://github.com/OptimalBits/bull/compare/v1.0.0-rc1...v1.0.0-rc2)

## v1.0.0-rc1

- Removed all potential dangerous hazards by atomizing many operations using
  cached LUA scripts.
- Improved performance around 400% compared to previous version.
- Better pause/resume (#266), and added pause for local workers.
- Fixed #272, #271, #261, #253, #240, #239

[Changes](https://github.com/OptimalBits/bull/compare/v0.7.2...v1.0.0-rc1)

## v0.7.2

- Added local pause/resume functionality
- fixed memory leaks present in the run promise chain.
- fixed "Illegal access to a strict mode caller function".

[Changes](https://github.com/OptimalBits/bull/compare/v0.7.1...v0.7.2)

## v0.7.1

- fixed storing of stacktraces

[Changes](https://github.com/OptimalBits/bull/compare/v0.7.0...v0.7.1)

## v0.7.0

- store the return value from the job handlers.
- store stacktraces.
- improvements in delayed jobs.

[Changes](https://github.com/OptimalBits/bull/compare/v0.6.0...v0.7.0)

## v0.4.0

- added a Queue##clean method

[Changes](https://github.com/OptimalBits/bull/compare/v0.3.0...v0.4.0)

## v0.3.0

- added support for custom clients.
- added test support for node 0.12.
- timeout improvements.
- unit test improvements.
- added timeout to queue pop blocking call.
- removed when dependency.

[Changes](https://github.com/OptimalBits/bull/compare/v0.2.7...v0.3.0)

## v0.2.7

[Changes](https://github.com/OptimalBits/bull/compare/v0.2.6...v0.2.7)

## v0.2.6

- [Fix] #103 When a queue start it do not process delayed job.
  [Changes](https://github.com/OptimalBits/bull/compare/v0.2.5...v0.2.6)

## v0.2.5

- [upgrade] Upgraded node redis to version 0.12.x
- [improvement] eslinted all code.
- [fix] added missing token when calling takeLock on jobs.

[Changes](https://github.com/OptimalBits/bull/compare/v0.2.4...v0.2.5)

## v0.2.4

[Changes](https://github.com/OptimalBits/bull/compare/v0.2.3...v0.2.4)

## v0.2.3

[Changes](https://github.com/OptimalBits/bull/compare/v0.1.9...v0.2.3)

## v0.1.9

- [Improvement] Faster job removal. (manast)

## v0.1.8

- [Improvement] Better promisification of redis methods. (manast)

## v0.1.7

- [Feature] Added a convenience method for getting a job. (loginx)
- [Fix] Only set a redis db from options if defined. (jboga)
- [Fix] Fixed issue #52. (manast)

## v0.1.6

- [Fix] Improved and corrected job's getters.
- [Fix] Automatically restart queues in the event of redis disconnections.
- [Feature] Added support for adding jobs in a LIFO fashion.
