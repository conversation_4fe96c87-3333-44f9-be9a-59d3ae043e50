const redis = require('redis');

/**
 * Redis configuration and connection management
 */

let redisClient = null;
let isRedisAvailable = false;

/**
 * Initialize Redis connection
 */
const initializeRedis = async () => {
  try {
    // Check if Redis is explicitly disabled
    if (process.env.DISABLE_REDIS === 'true') {
      console.log('[Redis] Redis disabled via environment variable');
      return;
    }

    // Check if Redis is configured
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

    console.log('[Redis] Attempting to connect to Redis...');

    // Create Redis client with better error handling
    redisClient = redis.createClient({
      url: redisUrl,
      socket: {
        connectTimeout: 5000, // 5 second timeout
        lazyConnect: true,    // Don't connect immediately
      },
      retry_strategy: (options) => {
        // Don't retry on connection refused (Redis not installed)
        if (options.error && options.error.code === 'ECONNREFUSED') {
          console.warn('[Redis] Redis server not available - continuing without Redis');
          return undefined;
        }
        // Don't retry after 3 attempts
        if (options.attempt > 3) {
          console.warn('[Redis] Max retry attempts reached - continuing without Redis');
          return undefined;
        }
        // Don't retry after 30 seconds total
        if (options.total_retry_time > 30000) {
          console.warn('[Redis] Retry timeout - continuing without Redis');
          return undefined;
        }
        return Math.min(options.attempt * 1000, 3000);
      }
    });

    // Handle Redis events with reduced logging
    redisClient.on('connect', () => {
      console.log('[Redis] ✅ Connected to Redis server');
      isRedisAvailable = true;
    });

    redisClient.on('error', (err) => {
      // Only log the first error to avoid spam
      if (isRedisAvailable) {
        console.warn('[Redis] ⚠️ Redis connection lost:', err.message);
      }
      isRedisAvailable = false;
    });

    redisClient.on('end', () => {
      if (isRedisAvailable) {
        console.log('[Redis] Redis connection ended');
      }
      isRedisAvailable = false;
    });

    // Try to connect with timeout
    const connectPromise = redisClient.connect();
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Connection timeout')), 5000)
    );

    await Promise.race([connectPromise, timeoutPromise]);

    // Test the connection
    await redisClient.ping();
    console.log('[Redis] ✅ Redis connection successful and tested');
    isRedisAvailable = true;

  } catch (error) {
    // Only log once to avoid spam
    if (error.code === 'ECONNREFUSED') {
      console.log('[Redis] ℹ️ Redis server not running - application will continue without caching');
    } else {
      console.warn('[Redis] ⚠️ Redis connection failed:', error.message);
      console.log('[Redis] ℹ️ Application will continue without Redis caching');
    }
    isRedisAvailable = false;
    redisClient = null;
  }
};

/**
 * Get Redis client instance
 */
const getRedisClient = () => {
  return redisClient;
};

/**
 * Check if Redis is available
 */
const isRedisConnected = () => {
  return isRedisAvailable && redisClient && redisClient.isOpen;
};

/**
 * Cache helper functions
 */
const cache = {
  /**
   * Set a value in cache with optional TTL
   */
  set: async (key, value, ttlSeconds = 3600) => {
    if (!isRedisConnected()) {
      return false;
    }

    try {
      const serializedValue = JSON.stringify(value);
      await redisClient.setEx(key, ttlSeconds, serializedValue);
      return true;
    } catch (error) {
      console.error('[Redis] Cache set error:', error);
      return false;
    }
  },

  /**
   * Get a value from cache
   */
  get: async (key) => {
    if (!isRedisConnected()) {
      return null;
    }

    try {
      const value = await redisClient.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('[Redis] Cache get error:', error);
      return null;
    }
  },

  /**
   * Delete a value from cache
   */
  del: async (key) => {
    if (!isRedisConnected()) {
      return false;
    }

    try {
      await redisClient.del(key);
      return true;
    } catch (error) {
      console.error('[Redis] Cache delete error:', error);
      return false;
    }
  },

  /**
   * Delete multiple keys matching a pattern
   */
  delPattern: async (pattern) => {
    if (!isRedisConnected()) {
      return false;
    }

    try {
      const keys = await redisClient.keys(pattern);
      if (keys.length > 0) {
        await redisClient.del(keys);
      }
      return true;
    } catch (error) {
      console.error('[Redis] Cache pattern delete error:', error);
      return false;
    }
  },

  /**
   * Check if a key exists in cache
   */
  exists: async (key) => {
    if (!isRedisConnected()) {
      return false;
    }

    try {
      const exists = await redisClient.exists(key);
      return exists === 1;
    } catch (error) {
      console.error('[Redis] Cache exists error:', error);
      return false;
    }
  },

  /**
   * Set TTL for an existing key
   */
  expire: async (key, ttlSeconds) => {
    if (!isRedisConnected()) {
      return false;
    }

    try {
      await redisClient.expire(key, ttlSeconds);
      return true;
    } catch (error) {
      console.error('[Redis] Cache expire error:', error);
      return false;
    }
  }
};

/**
 * Cache key generators
 */
const cacheKeys = {
  pdfPreview: (contentId) => `pdf_preview:${contentId}`,
  userProfile: (userId) => `user_profile:${userId}`,
  contentList: (params) => `content_list:${Buffer.from(JSON.stringify(params)).toString('base64')}`,
  dashboardStats: (userId, role) => `dashboard_stats:${role}:${userId}`,
  publicSettings: () => 'public_settings',
  apiResponse: (endpoint, params) => `api_response:${endpoint}:${Buffer.from(JSON.stringify(params)).toString('base64')}`
};

/**
 * Graceful shutdown
 */
const closeRedis = async () => {
  if (redisClient) {
    try {
      await redisClient.quit();
      console.log('[Redis] Redis connection closed gracefully');
    } catch (error) {
      console.error('[Redis] Error closing Redis connection:', error);
    }
  }
};

module.exports = {
  initializeRedis,
  getRedisClient,
  isRedisConnected,
  cache,
  cacheKeys,
  closeRedis
};
