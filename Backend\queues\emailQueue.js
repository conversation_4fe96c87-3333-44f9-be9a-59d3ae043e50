const Queue = require('bull');
const sendEmail = require('../utils/sendEmail');
const { isRedisConnected } = require('../config/redis');

/**
 * Email Queue System for asynchronous email processing
 */

// Create email queue with Redis connection or in-memory fallback
let emailQueue;

const initializeEmailQueue = () => {
  try {
    if (isRedisConnected()) {
      // Use Redis for queue if available
      emailQueue = new Queue('email processing', {
        redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: process.env.REDIS_PORT || 6379,
          password: process.env.REDIS_PASSWORD || undefined,
        },
        defaultJobOptions: {
          removeOnComplete: 50, // Keep last 50 completed jobs
          removeOnFail: 100,    // Keep last 100 failed jobs
          attempts: 3,          // Retry failed jobs 3 times
          backoff: {
            type: 'exponential',
            delay: 2000,        // Start with 2 second delay
          },
        },
      });
      
      console.log('[EmailQueue] Email queue initialized with Redis');
    } else {
      // Fallback to in-memory processing without Bull
      console.log('[EmailQueue] Redis not available, using direct email processing');
      emailQueue = null;
    }
  } catch (error) {
    console.error('[EmailQueue] Failed to initialize email queue:', error);
    emailQueue = null;
  }
};

// Email job processor
const processEmailJob = async (job) => {
  const { to, email, subject, message, html, priority = 'normal' } = job.data;
  
  try {
    console.log(`[EmailQueue] Processing email job: ${job.id} - ${subject}`);
    
    // Add job progress tracking
    job.progress(10);
    
    // Send email
    await sendEmail({
      to: to || email,
      subject,
      message,
      html
    });
    
    job.progress(100);
    console.log(`[EmailQueue] Email sent successfully: ${job.id}`);
    
    return { success: true, jobId: job.id, timestamp: new Date() };
  } catch (error) {
    console.error(`[EmailQueue] Email job failed: ${job.id}`, error);
    throw error; // This will trigger retry mechanism
  }
};

// Set up queue processing if Bull is available
if (emailQueue) {
  // Process jobs with concurrency
  emailQueue.process('send-email', 5, processEmailJob);
  
  // Queue event handlers
  emailQueue.on('completed', (job, result) => {
    console.log(`[EmailQueue] Job completed: ${job.id}`);
  });
  
  emailQueue.on('failed', (job, err) => {
    console.error(`[EmailQueue] Job failed: ${job.id}`, err.message);
  });
  
  emailQueue.on('stalled', (job) => {
    console.warn(`[EmailQueue] Job stalled: ${job.id}`);
  });
}

/**
 * Add email to queue or send directly
 */
const queueEmail = async (emailData, options = {}) => {
  const {
    priority = 'normal',
    delay = 0,
    attempts = 3,
    removeOnComplete = true,
    removeOnFail = false
  } = options;
  
  try {
    if (emailQueue) {
      // Use Bull queue
      const job = await emailQueue.add('send-email', emailData, {
        priority: getPriorityValue(priority),
        delay,
        attempts,
        removeOnComplete,
        removeOnFail,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      });
      
      console.log(`[EmailQueue] Email queued: ${job.id} - ${emailData.subject}`);
      return { success: true, jobId: job.id, queued: true };
    } else {
      // Direct email sending fallback
      console.log(`[EmailQueue] Sending email directly: ${emailData.subject}`);
      await sendEmail(emailData);
      return { success: true, queued: false };
    }
  } catch (error) {
    console.error('[EmailQueue] Failed to queue email:', error);
    throw error;
  }
};

/**
 * Convert priority string to Bull priority value
 */
const getPriorityValue = (priority) => {
  switch (priority.toLowerCase()) {
    case 'critical': return 1;
    case 'high': return 2;
    case 'normal': return 3;
    case 'low': return 4;
    default: return 3;
  }
};

/**
 * Queue email with high priority (authentication, payments, etc.)
 */
const queueHighPriorityEmail = async (emailData) => {
  return queueEmail(emailData, { 
    priority: 'high',
    attempts: 5,
    removeOnComplete: false 
  });
};

/**
 * Queue email with normal priority (notifications, updates, etc.)
 */
const queueNormalPriorityEmail = async (emailData) => {
  return queueEmail(emailData, { 
    priority: 'normal',
    attempts: 3 
  });
};

/**
 * Queue email with low priority (marketing, newsletters, etc.)
 */
const queueLowPriorityEmail = async (emailData) => {
  return queueEmail(emailData, { 
    priority: 'low',
    attempts: 2,
    delay: 5000 // 5 second delay
  });
};

/**
 * Queue bulk emails (batch processing)
 */
const queueBulkEmails = async (emailsArray) => {
  if (!emailQueue) {
    // Fallback: send emails directly with delay
    for (const emailData of emailsArray) {
      try {
        await sendEmail(emailData);
        // Add small delay to prevent overwhelming email server
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error('[EmailQueue] Bulk email failed:', error);
      }
    }
    return { success: true, queued: false, count: emailsArray.length };
  }
  
  const jobs = await Promise.all(
    emailsArray.map(emailData => 
      emailQueue.add('send-email', emailData, {
        priority: getPriorityValue('low'),
        attempts: 2,
        delay: Math.random() * 10000, // Random delay up to 10 seconds
      })
    )
  );
  
  console.log(`[EmailQueue] Bulk emails queued: ${jobs.length} emails`);
  return { success: true, queued: true, count: jobs.length, jobIds: jobs.map(j => j.id) };
};

/**
 * Get queue statistics
 */
const getQueueStats = async () => {
  if (!emailQueue) {
    return { available: false, message: 'Queue not available' };
  }
  
  try {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      emailQueue.getWaiting(),
      emailQueue.getActive(),
      emailQueue.getCompleted(),
      emailQueue.getFailed(),
      emailQueue.getDelayed(),
    ]);
    
    return {
      available: true,
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      total: waiting.length + active.length + completed.length + failed.length + delayed.length
    };
  } catch (error) {
    console.error('[EmailQueue] Error getting queue stats:', error);
    return { available: false, error: error.message };
  }
};

/**
 * Clean up completed and failed jobs
 */
const cleanQueue = async () => {
  if (!emailQueue) {
    return { success: false, message: 'Queue not available' };
  }
  
  try {
    await emailQueue.clean(24 * 60 * 60 * 1000, 'completed'); // Remove completed jobs older than 24 hours
    await emailQueue.clean(7 * 24 * 60 * 60 * 1000, 'failed'); // Remove failed jobs older than 7 days
    
    console.log('[EmailQueue] Queue cleaned successfully');
    return { success: true };
  } catch (error) {
    console.error('[EmailQueue] Error cleaning queue:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  initializeEmailQueue,
  queueEmail,
  queueHighPriorityEmail,
  queueNormalPriorityEmail,
  queueLowPriorityEmail,
  queueBulkEmails,
  getQueueStats,
  cleanQueue
};
