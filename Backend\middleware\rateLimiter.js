const rateLimit = require('express-rate-limit');

/**
 * Rate limiting configurations for different endpoints
 */

// General API rate limiter
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window per IP
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip static files and chunked upload endpoints
  skip: (req) => {
    // Skip static files
    if (req.url.startsWith('/uploads/') || req.url.startsWith('/static/')) {
      return true;
    }
    // Skip chunked upload endpoints (they have their own rate limiter)
    const isChunkedUpload = req.url.includes('/upload/chunk') ||
      req.url.includes('/upload/init') ||
      req.url.includes('/upload/complete') ||
      req.url.includes('/upload/status') ||
      req.url.includes('/upload/cancel');

    if (isChunkedUpload) {
      console.log(`[RateLimit] Skipping general limiter for chunked upload: ${req.method} ${req.url}`);
      return true;
    }
    return false;
  }
});

// Strict rate limiter for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window per IP
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Reset on successful login
  skipSuccessfulRequests: true
});

// Payment endpoints rate limiter
const paymentLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // 10 payment attempts per window per IP
  message: {
    error: 'Too many payment attempts, please try again later.',
    retryAfter: '5 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// File upload rate limiter (for complete file uploads)
const uploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 uploads per hour per IP
  message: {
    error: 'Upload limit exceeded, please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip for chunked uploads, profile images, and small files
  skip: (req) => {
    // Skip chunked upload endpoints
    if (req.url.includes('/chunk') || req.url.includes('/chunked')) {
      return true;
    }
    // Skip small profile images
    if (req.url.includes('/profile') && req.headers['content-length'] < 5 * 1024 * 1024) {
      return true;
    }
    return false;
  }
});

// Chunked upload rate limiter (temporarily disabled for testing)
const chunkedUploadLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 10000, // 10000 chunks per minute (effectively unlimited for testing)
  message: {
    error: 'Too many chunk uploads, please slow down.',
    retryAfter: '1 minute'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip all requests for now to test if this is the issue
  skip: (req) => {
    console.log(`[RateLimit] Chunked upload limiter called for: ${req.method} ${req.url}`);
    return true; // Skip all rate limiting for chunked uploads temporarily
  },
  onLimitReached: (req) => {
    console.log(`[RateLimit] Chunked upload limit reached for IP: ${req.ip}, URL: ${req.url}`);
  }
});

// Admin endpoints rate limiter (more lenient for admin users)
const adminLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // 200 requests per window per IP
  message: {
    error: 'Admin rate limit exceeded, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Email sending rate limiter
const emailLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 emails per hour per IP
  message: {
    error: 'Email sending limit exceeded, please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// OTP rate limiter
const otpLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // 3 OTP requests per window per IP
  message: {
    error: 'Too many OTP requests, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

module.exports = {
  generalLimiter,
  authLimiter,
  paymentLimiter,
  uploadLimiter,
  chunkedUploadLimiter,
  adminLimiter,
  emailLimiter,
  otpLimiter
};
