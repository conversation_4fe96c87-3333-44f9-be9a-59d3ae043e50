const rateLimit = require('express-rate-limit');

/**
 * Rate limiting configurations for different endpoints
 */

// General API rate limiter
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window per IP
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip successful requests to static files
  skip: (req) => {
    return req.url.startsWith('/uploads/') || req.url.startsWith('/static/');
  }
});

// Strict rate limiter for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window per IP
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Reset on successful login
  skipSuccessfulRequests: true
});

// Payment endpoints rate limiter
const paymentLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // 10 payment attempts per window per IP
  message: {
    error: 'Too many payment attempts, please try again later.',
    retryAfter: '5 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// File upload rate limiter (for complete file uploads)
const uploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 uploads per hour per IP
  message: {
    error: 'Upload limit exceeded, please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip for chunked uploads, profile images, and small files
  skip: (req) => {
    // Skip chunked upload endpoints
    if (req.url.includes('/chunk') || req.url.includes('/chunked')) {
      return true;
    }
    // Skip small profile images
    if (req.url.includes('/profile') && req.headers['content-length'] < 5 * 1024 * 1024) {
      return true;
    }
    return false;
  }
});

// Chunked upload rate limiter (more permissive for chunks)
const chunkedUploadLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 1000, // 1000 chunks per 5 minutes per IP (allows large video uploads)
  message: {
    error: 'Too many chunk uploads, please slow down.',
    retryAfter: '5 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Custom key generator to allow multiple concurrent chunked uploads
  keyGenerator: (req) => {
    // Use IP + upload session ID if available
    const uploadId = req.body?.uploadId || req.query?.uploadId || 'default';
    return `${req.ip}-chunked-${uploadId}`;
  }
});

// Admin endpoints rate limiter (more lenient for admin users)
const adminLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // 200 requests per window per IP
  message: {
    error: 'Admin rate limit exceeded, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Email sending rate limiter
const emailLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 emails per hour per IP
  message: {
    error: 'Email sending limit exceeded, please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// OTP rate limiter
const otpLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // 3 OTP requests per window per IP
  message: {
    error: 'Too many OTP requests, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

module.exports = {
  generalLimiter,
  authLimiter,
  paymentLimiter,
  uploadLimiter,
  chunkedUploadLimiter,
  adminLimiter,
  emailLimiter,
  otpLimiter
};
